<!-- Recent Activity Feed Component -->
<div class="space-y-4">
    {% for user in recent_users %}
        <div class="flex items-start space-x-3">
            <div class="flex-shrink-0">
                <div class="w-8 h-8 bg-blue-100 rounded-full flex items-center justify-center">
                    <svg class="w-4 h-4 text-blue-600" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                        <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M16 7a4 4 0 11-8 0 4 4 0 018 0zM12 14a7 7 0 00-7 7h14a7 7 0 00-7-7z" />
                    </svg>
                </div>
            </div>
            <div class="flex-1 min-w-0">
                <p class="text-sm text-gray-900">
                    <span class="font-medium">{{ user.get_full_name|default:user.username }}</span> 
                    registered as a new {{ user.profile.get_role_display|lower }}
                </p>
                <p class="text-xs text-gray-500">{{ user.date_joined|timesince }} ago</p>
            </div>
        </div>
    {% endfor %}

    {% for listing in recent_listings %}
        <div class="flex items-start space-x-3">
            <div class="flex-shrink-0">
                <div class="w-8 h-8 bg-green-100 rounded-full flex items-center justify-center">
                    <svg class="w-4 h-4 text-green-600" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                        <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M12 6v6m0 0v6m0-6h6m-6 0H6" />
                    </svg>
                </div>
            </div>
            <div class="flex-1 min-w-0">
                <p class="text-sm text-gray-900">
                    <span class="font-medium">{{ listing.owner.get_full_name|default:listing.owner.username }}</span> 
                    created a new listing
                    <span class="font-medium">"{{ listing.title|truncatechars:30 }}"</span>
                </p>
                <p class="text-xs text-gray-500">{{ listing.created_at|timesince }} ago</p>
            </div>
        </div>
    {% endfor %}

    {% for inquiry in recent_inquiries %}
        <div class="flex items-start space-x-3">
            <div class="flex-shrink-0">
                <div class="w-8 h-8 bg-yellow-100 rounded-full flex items-center justify-center">
                    <svg class="w-4 h-4 text-yellow-600" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                        <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M8 12h.01M12 12h.01M16 12h.01M21 12c0 4.418-4.03 8-9 8a9.863 9.863 0 01-4.255-.949L3 20l1.395-3.72C3.512 15.042 3 13.574 3 12c0-4.418 4.03-8 9-8s9 3.582 9 8z" />
                    </svg>
                </div>
            </div>
            <div class="flex-1 min-w-0">
                <p class="text-sm text-gray-900">
                    <span class="font-medium">{{ inquiry.buyer.get_full_name|default:inquiry.buyer.username }}</span>
                    sent an inquiry for 
                    <span class="font-medium">"{{ inquiry.land.title|truncatechars:30 }}"</span>
                </p>
                <p class="text-xs text-gray-500">{{ inquiry.created_at|timesince }} ago</p>
            </div>
        </div>
    {% endfor %}

    {% if not recent_users and not recent_listings and not recent_inquiries %}
        <div class="text-center py-8">
            <svg class="mx-auto h-12 w-12 text-gray-400" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M12 8v4l3 3m6-3a9 9 0 11-18 0 9 9 0 0118 0z" />
            </svg>
            <h3 class="mt-2 text-sm font-medium text-gray-900">No recent activity</h3>
            <p class="mt-1 text-sm text-gray-500">Activity will appear here as users interact with the platform.</p>
        </div>
    {% endif %}
</div>
