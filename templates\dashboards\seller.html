{% extends 'base.html' %}
{% load static %}

{% block title %}Seller Dashboard - LandHub{% endblock %}
{% block meta_description %}LandHub Seller Dashboard - Manage your listings, track inquiries, and grow your land sales{% endblock %}

{% block extra_css %}
<style>
    /* Custom seller dashboard styles - consistent with admin dashboard */
    .admin-card {
        background: linear-gradient(135deg, rgba(255,255,255,0.9) 0%, rgba(255,255,255,0.95) 100%);
        backdrop-filter: blur(10px);
        border: 1px solid rgba(255,255,255,0.2);
    }
    
    .metric-card {
        transition: all 0.3s ease;
    }
    
    .metric-card:hover {
        transform: translateY(-2px);
        box-shadow: 0 10px 25px rgba(0,0,0,0.1);
    }
    
    .chart-bar {
        transition: all 0.3s ease;
    }
    
    .chart-bar:hover {
        opacity: 0.8;
        transform: scaleY(1.05);
    }
    
    .listing-card {
        transition: all 0.3s ease;
    }
    
    .listing-card:hover {
        transform: translateY(-1px);
        box-shadow: 0 8px 20px rgba(0,0,0,0.08);
    }
</style>
{% endblock %}

{% block content %}
<!-- Seller Dashboard Content -->
<div class="bg-gradient-to-br from-gray-50 to-gray-100 min-h-screen" x-data="sellerDashboard()">
    <!-- Dashboard Header -->
    <div class="bg-white border-b border-gray-200 shadow-sm">
        <div class="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
            <div class="flex items-center justify-between py-6">
                <div>
                    <h1 class="text-2xl font-bold text-gray-900">Seller Dashboard</h1>
                    <p class="mt-1 text-sm text-gray-600">Welcome back, {{ user.get_full_name|default:user.username }}. Manage your listings and track your sales performance.</p>
                </div>
                <div class="flex items-center space-x-3">
                    <!-- Refresh Button -->
                    <button @click="refreshData()" 
                            :disabled="loading"
                            class="inline-flex items-center px-4 py-2 border border-gray-300 rounded-lg text-sm font-medium text-gray-700 bg-white hover:bg-gray-50 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-primary-500 disabled:opacity-50 transition-all duration-200">
                        <svg class="w-4 h-4 mr-2" :class="{ 'animate-spin': loading }" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M4 4v5h.582m15.356 2A8.001 8.001 0 004.582 9m0 0H9m11 11v-5h-.581m0 0a8.003 8.003 0 01-15.357-2m15.357 2H15" />
                        </svg>
                        Refresh
                    </button>
                    <!-- Quick Actions Dropdown -->
                    <div class="relative" x-data="{ open: false }">
                        <button @click="open = !open" 
                                class="inline-flex items-center px-4 py-2 bg-primary-600 text-white rounded-lg text-sm font-medium hover:bg-primary-700 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-primary-500 transition-all duration-200 shadow-sm">
                            Quick Actions
                            <svg class="w-4 h-4 ml-2" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M19 9l-7 7-7-7" />
                            </svg>
                        </button>
                        <div x-show="open" 
                             x-transition:enter="transition ease-out duration-100"
                             x-transition:enter-start="transform opacity-0 scale-95"
                             x-transition:enter-end="transform opacity-100 scale-100"
                             x-transition:leave="transition ease-in duration-75"
                             x-transition:leave-start="transform opacity-100 scale-100"
                             x-transition:leave-end="transform opacity-0 scale-95"
                             @click.away="open = false"
                             class="absolute right-0 mt-2 w-56 bg-white rounded-lg shadow-lg border border-gray-200 z-10">
                            <div class="py-1">
                                <a href="#" class="block px-4 py-2 text-sm text-gray-700 hover:bg-gray-100 transition-colors duration-150">Create New Listing</a>
                                <a href="#" class="block px-4 py-2 text-sm text-gray-700 hover:bg-gray-100 transition-colors duration-150">Bulk Edit Listings</a>
                                <a href="#" class="block px-4 py-2 text-sm text-gray-700 hover:bg-gray-100 transition-colors duration-150">Export Data</a>
                                <div class="border-t border-gray-100"></div>
                                <a href="#" class="block px-4 py-2 text-sm text-gray-700 hover:bg-gray-100 transition-colors duration-150">Account Settings</a>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>

    <!-- Main Dashboard Content -->
    <div class="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8 py-8">
        <!-- Key Metrics Cards -->
        <div class="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-6 mb-8">
            <!-- Total Listings Card -->
            <div class="admin-card metric-card rounded-xl shadow-sm p-6">
                <div class="flex items-center">
                    <div class="flex-shrink-0">
                        <div class="w-12 h-12 bg-gradient-to-br from-blue-500 to-blue-600 rounded-xl flex items-center justify-center shadow-lg">
                            <svg class="w-6 h-6 text-white" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M19 21V5a2 2 0 00-2-2H7a2 2 0 00-2 2v16m14 0h2m-2 0h-5m-9 0H3m2 0h5M9 7h1m-1 4h1m4-4h1m-1 4h1m-5 10v-5a1 1 0 011-1h2a1 1 0 011 1v5m-4 0h4" />
                            </svg>
                        </div>
                    </div>
                    <div class="ml-4 flex-1">
                        <p class="text-sm font-medium text-gray-600">Total Listings</p>
                        <div class="flex items-baseline">
                            <p class="text-2xl font-bold text-gray-900">{{ total_listings|default:0 }}</p>
                            <p class="ml-2 text-sm font-medium text-green-600 bg-green-50 px-2 py-1 rounded-full">+3 this month</p>
                        </div>
                    </div>
                </div>
                <div class="mt-4 pt-4 border-t border-gray-100">
                    <div class="flex items-center justify-between text-sm text-gray-500">
                        <span>Active: {{ active_listings|default:0 }}</span>
                        <span>Draft: {{ draft_listings|default:0 }}</span>
                    </div>
                </div>
            </div>

            <!-- Total Views Card -->
            <div class="admin-card metric-card rounded-xl shadow-sm p-6">
                <div class="flex items-center">
                    <div class="flex-shrink-0">
                        <div class="w-12 h-12 bg-gradient-to-br from-green-500 to-emerald-600 rounded-xl flex items-center justify-center shadow-lg">
                            <svg class="w-6 h-6 text-white" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M15 12a3 3 0 11-6 0 3 3 0 016 0z" />
                                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M2.458 12C3.732 7.943 7.523 5 12 5c4.478 0 8.268 2.943 9.542 7-1.274 4.057-5.064 7-9.542 7-4.477 0-8.268-2.943-9.542-7z" />
                            </svg>
                        </div>
                    </div>
                    <div class="ml-4 flex-1">
                        <p class="text-sm font-medium text-gray-600">Total Views</p>
                        <div class="flex items-baseline">
                            <p class="text-2xl font-bold text-gray-900">{{ total_views|default:0 }}</p>
                            <p class="ml-2 text-sm font-medium text-green-600 bg-green-50 px-2 py-1 rounded-full">+12%</p>
                        </div>
                    </div>
                </div>
                <div class="mt-4 pt-4 border-t border-gray-100">
                    <div class="flex items-center text-sm text-gray-500">
                        <span>This month: {{ monthly_views|default:0 }}</span>
                    </div>
                </div>
            </div>

            <!-- Inquiries Received Card -->
            <div class="admin-card metric-card rounded-xl shadow-sm p-6">
                <div class="flex items-center">
                    <div class="flex-shrink-0">
                        <div class="w-12 h-12 bg-gradient-to-br from-purple-500 to-indigo-600 rounded-xl flex items-center justify-center shadow-lg">
                            <svg class="w-6 h-6 text-white" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M3 8l7.89 5.26a2 2 0 002.22 0L21 8M5 19h14a2 2 0 002-2V7a2 2 0 00-2-2H5a2 2 0 00-2 2v10a2 2 0 002 2z" />
                            </svg>
                        </div>
                    </div>
                    <div class="ml-4 flex-1">
                        <p class="text-sm font-medium text-gray-600">Inquiries Received</p>
                        <div class="flex items-baseline">
                            <p class="text-2xl font-bold text-gray-900">{{ total_inquiries|default:0 }}</p>
                            {% if pending_inquiries > 0 %}
                                <span class="ml-2 inline-flex items-center px-2.5 py-0.5 rounded-full text-xs font-medium bg-blue-100 text-blue-800">
                                    {{ pending_inquiries }} new
                                </span>
                            {% endif %}
                        </div>
                    </div>
                </div>
                <div class="mt-4 pt-4 border-t border-gray-100">
                    <div class="flex items-center text-sm text-gray-500">
                        <span>Response rate: {{ response_rate|default:0 }}%</span>
                    </div>
                </div>
            </div>

            <!-- Average Price Card -->
            <div class="admin-card metric-card rounded-xl shadow-sm p-6">
                <div class="flex items-center">
                    <div class="flex-shrink-0">
                        <div class="w-12 h-12 bg-gradient-to-br from-yellow-500 to-orange-500 rounded-xl flex items-center justify-center shadow-lg">
                            <svg class="w-6 h-6 text-white" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M12 8c-1.657 0-3 .895-3 2s1.343 2 3 2 3 .895 3 2-1.343 2-3 2m0-8c1.11 0 2.08.402 2.599 1M12 8V7m0 1v8m0 0v1m0-1c-1.11 0-2.08-.402-2.599-1" />
                            </svg>
                        </div>
                    </div>
                    <div class="ml-4 flex-1">
                        <p class="text-sm font-medium text-gray-600">Average Price</p>
                        <div class="flex items-baseline">
                            <p class="text-2xl font-bold text-gray-900">${{ average_price|default:0|floatformat:0 }}</p>
                            <p class="ml-2 text-sm font-medium text-green-600 bg-green-50 px-2 py-1 rounded-full">+5%</p>
                        </div>
                    </div>
                </div>
                <div class="mt-4 pt-4 border-t border-gray-100">
                    <div class="flex items-center text-sm text-gray-500">
                        <span>Per acre: ${{ price_per_acre|default:0|floatformat:0 }}</span>
                    </div>
                </div>
            </div>
        </div>

        <!-- Charts and Analytics Section -->
        <div class="grid grid-cols-1 lg:grid-cols-2 gap-8 mb-8">
            <!-- Listing Performance Chart -->
            <div class="admin-card rounded-xl shadow-sm p-6">
                <div class="flex items-center justify-between mb-6">
                    <h3 class="text-lg font-semibold text-gray-900">Listing Performance</h3>
                    <div class="flex items-center space-x-1 bg-gray-100 rounded-lg p-1">
                        <button class="text-sm text-gray-500 hover:text-gray-700 px-3 py-1 rounded-md hover:bg-white transition-all duration-150">7D</button>
                        <button class="text-sm text-white bg-primary-600 px-3 py-1 rounded-md shadow-sm">30D</button>
                        <button class="text-sm text-gray-500 hover:text-gray-700 px-3 py-1 rounded-md hover:bg-white transition-all duration-150">90D</button>
                    </div>
                </div>
                <div class="h-64 flex items-end justify-between space-x-2">
                    <!-- Enhanced Bar Chart for Views -->
                    <div class="chart-bar flex-1 bg-gradient-to-t from-blue-500 to-blue-400 rounded-t-lg shadow-sm" style="height: 45%"></div>
                    <div class="chart-bar flex-1 bg-gradient-to-t from-blue-500 to-blue-400 rounded-t-lg shadow-sm" style="height: 65%"></div>
                    <div class="chart-bar flex-1 bg-gradient-to-t from-blue-600 to-blue-500 rounded-t-lg shadow-sm" style="height: 85%"></div>
                    <div class="chart-bar flex-1 bg-gradient-to-t from-blue-500 to-blue-400 rounded-t-lg shadow-sm" style="height: 75%"></div>
                    <div class="chart-bar flex-1 bg-gradient-to-t from-blue-600 to-blue-500 rounded-t-lg shadow-sm" style="height: 95%"></div>
                    <div class="chart-bar flex-1 bg-gradient-to-t from-blue-700 to-blue-600 rounded-t-lg shadow-sm" style="height: 100%"></div>
                    <div class="chart-bar flex-1 bg-gradient-to-t from-blue-600 to-blue-500 rounded-t-lg shadow-sm" style="height: 90%"></div>
                </div>
                <div class="flex justify-between text-xs text-gray-500 mt-4 px-1">
                    <span>Week 1</span>
                    <span>Week 2</span>
                    <span>Week 3</span>
                    <span>Week 4</span>
                </div>
            </div>

            <!-- Property Types Distribution -->
            <div class="admin-card rounded-xl shadow-sm p-6">
                <h3 class="text-lg font-semibold text-gray-900 mb-6">Your Property Types</h3>
                <div class="space-y-5">
                    <div class="flex items-center justify-between">
                        <div class="flex items-center">
                            <div class="w-4 h-4 bg-gradient-to-r from-green-500 to-emerald-600 rounded-full mr-3 shadow-sm"></div>
                            <span class="text-sm font-medium text-gray-700">Agricultural</span>
                        </div>
                        <div class="flex items-center">
                            <div class="w-32 bg-gray-200 rounded-full h-3 mr-3 overflow-hidden">
                                <div class="bg-gradient-to-r from-green-500 to-emerald-600 h-3 rounded-full transition-all duration-500 shadow-sm" style="width: 60%"></div>
                            </div>
                            <span class="text-sm font-bold text-gray-900 min-w-[3rem] text-right">60%</span>
                        </div>
                    </div>
                    <div class="flex items-center justify-between">
                        <div class="flex items-center">
                            <div class="w-4 h-4 bg-gradient-to-r from-blue-500 to-blue-600 rounded-full mr-3 shadow-sm"></div>
                            <span class="text-sm font-medium text-gray-700">Residential</span>
                        </div>
                        <div class="flex items-center">
                            <div class="w-32 bg-gray-200 rounded-full h-3 mr-3 overflow-hidden">
                                <div class="bg-gradient-to-r from-blue-500 to-blue-600 h-3 rounded-full transition-all duration-500 shadow-sm" style="width: 25%"></div>
                            </div>
                            <span class="text-sm font-bold text-gray-900 min-w-[3rem] text-right">25%</span>
                        </div>
                    </div>
                    <div class="flex items-center justify-between">
                        <div class="flex items-center">
                            <div class="w-4 h-4 bg-gradient-to-r from-orange-500 to-red-500 rounded-full mr-3 shadow-sm"></div>
                            <span class="text-sm font-medium text-gray-700">Recreational</span>
                        </div>
                        <div class="flex items-center">
                            <div class="w-32 bg-gray-200 rounded-full h-3 mr-3 overflow-hidden">
                                <div class="bg-gradient-to-r from-orange-500 to-red-500 h-3 rounded-full transition-all duration-500 shadow-sm" style="width: 10%"></div>
                            </div>
                            <span class="text-sm font-bold text-gray-900 min-w-[3rem] text-right">10%</span>
                        </div>
                    </div>
                    <div class="flex items-center justify-between">
                        <div class="flex items-center">
                            <div class="w-4 h-4 bg-gradient-to-r from-purple-500 to-indigo-600 rounded-full mr-3 shadow-sm"></div>
                            <span class="text-sm font-medium text-gray-700">Commercial</span>
                        </div>
                        <div class="flex items-center">
                            <div class="w-32 bg-gray-200 rounded-full h-3 mr-3 overflow-hidden">
                                <div class="bg-gradient-to-r from-purple-500 to-indigo-600 h-3 rounded-full transition-all duration-500 shadow-sm" style="width: 5%"></div>
                            </div>
                            <span class="text-sm font-bold text-gray-900 min-w-[3rem] text-right">5%</span>
                        </div>
                    </div>
                </div>
            </div>
        </div>

        <!-- Recent Activity and Management Section -->
        <div class="grid grid-cols-1 lg:grid-cols-3 gap-8">
            <!-- Recent Activity Feed -->
            <div class="lg:col-span-2 admin-card rounded-xl shadow-sm">
                <div class="px-6 py-4 border-b border-gray-100">
                    <div class="flex items-center justify-between">
                        <h3 class="text-lg font-semibold text-gray-900">Recent Activity</h3>
                        <button onclick="window.location.reload()"
                                class="text-sm text-primary-600 hover:text-primary-500 font-medium bg-primary-50 px-3 py-1 rounded-lg hover:bg-primary-100 transition-all duration-150">
                            <svg class="w-4 h-4 inline mr-1" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M4 4v5h.582m15.356 2A8.001 8.001 0 004.582 9m0 0H9m11 11v-5h-.581m0 0a8.003 8.003 0 01-15.357-2m15.357 2H15" />
                            </svg>
                            Refresh
                        </button>
                    </div>
                </div>

                <div id="activity-feed" class="p-6">

                    <!-- Sample Activity Items -->
                    <div class="space-y-4">
                        <div class="flex items-start space-x-3 p-4 bg-blue-50 rounded-lg">
                            <div class="flex-shrink-0">
                                <div class="w-8 h-8 bg-blue-500 rounded-full flex items-center justify-center">
                                    <svg class="w-4 h-4 text-white" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                                        <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M3 8l7.89 5.26a2 2 0 002.22 0L21 8M5 19h14a2 2 0 002-2V7a2 2 0 00-2-2H5a2 2 0 00-2 2v10a2 2 0 002 2z" />
                                    </svg>
                                </div>
                            </div>
                            <div class="flex-1 min-w-0">
                                <p class="text-sm font-medium text-gray-900">New inquiry received</p>
                                <p class="text-sm text-gray-500">John Smith is interested in your 50-acre agricultural property in Texas</p>
                                <p class="text-xs text-gray-400 mt-1">2 hours ago</p>
                            </div>
                        </div>

                        <div class="flex items-start space-x-3 p-4 bg-green-50 rounded-lg">
                            <div class="flex-shrink-0">
                                <div class="w-8 h-8 bg-green-500 rounded-full flex items-center justify-center">
                                    <svg class="w-4 h-4 text-white" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                                        <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M15 12a3 3 0 11-6 0 3 3 0 016 0z" />
                                        <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M2.458 12C3.732 7.943 7.523 5 12 5c4.478 0 8.268 2.943 9.542 7-1.274 4.057-5.064 7-9.542 7-4.477 0-8.268-2.943-9.542-7z" />
                                    </svg>
                                </div>
                            </div>
                            <div class="flex-1 min-w-0">
                                <p class="text-sm font-medium text-gray-900">Listing viewed</p>
                                <p class="text-sm text-gray-500">Your residential property listing received 15 new views today</p>
                                <p class="text-xs text-gray-400 mt-1">4 hours ago</p>
                            </div>
                        </div>

                        <div class="flex items-start space-x-3 p-4 bg-yellow-50 rounded-lg">
                            <div class="flex-shrink-0">
                                <div class="w-8 h-8 bg-yellow-500 rounded-full flex items-center justify-center">
                                    <svg class="w-4 h-4 text-white" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                                        <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M12 8v4l3 3m6-3a9 9 0 11-18 0 9 9 0 0118 0z" />
                                    </svg>
                                </div>
                            </div>
                            <div class="flex-1 min-w-0">
                                <p class="text-sm font-medium text-gray-900">Listing needs attention</p>
                                <p class="text-sm text-gray-500">Your draft listing "Mountain View Ranch" is ready for review</p>
                                <p class="text-xs text-gray-400 mt-1">1 day ago</p>
                            </div>
                        </div>
                    </div>
                </div>
            </div>

            <!-- Quick Management Panel -->
            <div class="space-y-6">
                <!-- Pending Inquiries Management -->
                <div class="admin-card rounded-xl shadow-sm p-6">
                    <div class="flex items-center justify-between mb-4">
                        <h3 class="text-lg font-semibold text-gray-900">Pending Inquiries</h3>
                        <span class="inline-flex items-center px-3 py-1 rounded-full text-xs font-bold bg-gradient-to-r from-blue-500 to-blue-600 text-white shadow-sm">
                            {{ pending_inquiries|default:0 }}
                        </span>
                    </div>
                    <div class="space-y-3">
                        <div class="flex items-center justify-between p-3 bg-blue-50 rounded-lg">
                            <div class="flex items-center">
                                <div class="w-8 h-8 bg-blue-500 rounded-full flex items-center justify-center mr-3">
                                    <span class="text-white text-xs font-bold">JS</span>
                                </div>
                                <div>
                                    <p class="text-sm font-medium text-gray-900">John Smith</p>
                                    <p class="text-xs text-gray-500">50-acre Agricultural</p>
                                </div>
                            </div>
                            <button class="text-xs bg-blue-600 text-white px-3 py-1 rounded-full hover:bg-blue-700 transition-colors duration-150">
                                Reply
                            </button>
                        </div>
                        <div class="flex items-center justify-between p-3 bg-green-50 rounded-lg">
                            <div class="flex items-center">
                                <div class="w-8 h-8 bg-green-500 rounded-full flex items-center justify-center mr-3">
                                    <span class="text-white text-xs font-bold">MD</span>
                                </div>
                                <div>
                                    <p class="text-sm font-medium text-gray-900">Mary Davis</p>
                                    <p class="text-xs text-gray-500">25-acre Residential</p>
                                </div>
                            </div>
                            <button class="text-xs bg-green-600 text-white px-3 py-1 rounded-full hover:bg-green-700 transition-colors duration-150">
                                Reply
                            </button>
                        </div>
                    </div>
                    <div class="mt-4 pt-4 border-t border-gray-100">
                        <a href="#" class="text-sm font-medium text-primary-600 hover:text-primary-500 transition-colors duration-150">View all inquiries →</a>
                    </div>
                </div>

                <!-- Draft Listings Management -->
                <div class="admin-card rounded-xl shadow-sm p-6">
                    <div class="flex items-center justify-between mb-4">
                        <h3 class="text-lg font-semibold text-gray-900">Draft Listings</h3>
                        <span class="inline-flex items-center px-3 py-1 rounded-full text-xs font-bold bg-gradient-to-r from-yellow-500 to-orange-500 text-white shadow-sm">
                            {{ draft_listings|default:0 }}
                        </span>
                    </div>
                    <div class="space-y-3">
                        <div class="flex items-center justify-between p-3 bg-yellow-50 rounded-lg">
                            <div>
                                <p class="text-sm font-medium text-gray-900">Mountain View Ranch</p>
                                <p class="text-xs text-gray-500">100 acres • Recreational</p>
                            </div>
                            <button class="text-xs bg-yellow-600 text-white px-3 py-1 rounded-full hover:bg-yellow-700 transition-colors duration-150">
                                Edit
                            </button>
                        </div>
                        <div class="flex items-center justify-between p-3 bg-orange-50 rounded-lg">
                            <div>
                                <p class="text-sm font-medium text-gray-900">Farmland Opportunity</p>
                                <p class="text-xs text-gray-500">200 acres • Agricultural</p>
                            </div>
                            <button class="text-xs bg-orange-600 text-white px-3 py-1 rounded-full hover:bg-orange-700 transition-colors duration-150">
                                Edit
                            </button>
                        </div>
                    </div>
                    <div class="mt-4 pt-4 border-t border-gray-100">
                        <a href="#" class="text-sm font-medium text-primary-600 hover:text-primary-500 transition-colors duration-150">Manage drafts →</a>
                    </div>
                </div>

                <!-- Quick Stats -->
                <div class="admin-card rounded-xl shadow-sm p-6">
                    <h3 class="text-lg font-semibold text-gray-900 mb-4">Today's Summary</h3>
                    <div class="space-y-4">
                        <div class="flex items-center justify-between p-3 bg-blue-50 rounded-lg">
                            <span class="text-sm font-medium text-gray-700">New Views</span>
                            <span class="text-lg font-bold text-blue-700">{{ daily_views|default:0 }}</span>
                        </div>
                        <div class="flex items-center justify-between p-3 bg-green-50 rounded-lg">
                            <span class="text-sm font-medium text-gray-700">New Inquiries</span>
                            <span class="text-lg font-bold text-green-700">{{ daily_inquiries|default:0 }}</span>
                        </div>
                        <div class="flex items-center justify-between p-3 bg-purple-50 rounded-lg">
                            <span class="text-sm font-medium text-gray-700">Profile Views</span>
                            <span class="text-lg font-bold text-purple-700">{{ daily_profile_views|default:0 }}</span>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>
</div>

<script>
function sellerDashboard() {
    return {
        loading: false,
        refreshData() {
            this.loading = true;
            // Simulate API call
            setTimeout(() => {
                this.loading = false;
                // Refresh dashboard data
                window.location.reload();
            }, 1000);
        }
    }
}
</script>
{% endblock %}
