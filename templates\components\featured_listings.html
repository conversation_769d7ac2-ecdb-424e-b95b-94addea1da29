<!-- Featured Listings Grid -->
<div class="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-8">
    {% for listing in featured_listings %}
        <div class="bg-white rounded-lg shadow-md overflow-hidden hover:shadow-lg transition-shadow duration-300">
            <!-- Property Image -->
            <div class="relative h-48 bg-gray-200">
                {% if listing.images.exists %}
                    {% with listing.images.first as primary_image %}
                        <img src="{{ primary_image.image.url }}" 
                             alt="{{ primary_image.alt_text|default:listing.title }}"
                             class="w-full h-full object-cover">
                    {% endwith %}
                {% else %}
                    <!-- Placeholder image -->
                    <div class="w-full h-full flex items-center justify-center bg-gray-300">
                        <svg class="h-12 w-12 text-gray-400" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M4 16l4.586-4.586a2 2 0 012.828 0L16 16m-2-2l1.586-1.586a2 2 0 012.828 0L20 14m-6-6h.01M6 20h12a2 2 0 002-2V6a2 2 0 00-2-2H6a2 2 0 00-2 2v12a2 2 0 002 2z" />
                        </svg>
                    </div>
                {% endif %}
                
                <!-- Property Type Badge -->
                <div class="absolute top-3 left-3">
                    <span class="inline-flex items-center px-2.5 py-0.5 rounded-full text-xs font-medium
                        {% if listing.property_type == 'residential' %}bg-blue-100 text-blue-800
                        {% elif listing.property_type == 'commercial' %}bg-purple-100 text-purple-800
                        {% elif listing.property_type == 'agricultural' %}bg-green-100 text-green-800
                        {% elif listing.property_type == 'recreational' %}bg-orange-100 text-orange-800
                        {% else %}bg-gray-100 text-gray-800{% endif %}">
                        {{ listing.get_property_type_display }}
                    </span>
                </div>
                
                <!-- Favorite Button -->
                {% if user.is_authenticated and user.profile.role == 'buyer' %}
                    <button class="absolute top-3 right-3 p-2 rounded-full bg-white bg-opacity-80 hover:bg-opacity-100 transition-all duration-200"
                            hx-post="/api/toggle-favorite/{{ listing.id }}/"
                            hx-target="#favorite-{{ listing.id }}"
                            hx-swap="outerHTML">
                        <div id="favorite-{{ listing.id }}">
                            {% if listing in user.favorites.all %}
                                <svg class="h-5 w-5 text-red-500" fill="currentColor" viewBox="0 0 20 20">
                                    <path fill-rule="evenodd" d="M3.172 5.172a4 4 0 015.656 0L10 6.343l1.172-1.171a4 4 0 115.656 5.656L10 17.657l-6.828-6.829a4 4 0 010-5.656z" clip-rule="evenodd" />
                                </svg>
                            {% else %}
                                <svg class="h-5 w-5 text-gray-400 hover:text-red-500" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                                    <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M4.318 6.318a4.5 4.5 0 000 6.364L12 20.364l7.682-7.682a4.5 4.5 0 00-6.364-6.364L12 7.636l-1.318-1.318a4.5 4.5 0 00-6.364 0z" />
                                </svg>
                            {% endif %}
                        </div>
                    </button>
                {% endif %}
            </div>
            
            <!-- Property Details -->
            <div class="p-6">
                <div class="flex items-start justify-between mb-2">
                    <h3 class="text-lg font-semibold text-gray-900 line-clamp-2">{{ listing.title }}</h3>
                </div>
                
                <p class="text-gray-600 text-sm mb-3 line-clamp-2">{{ listing.description }}</p>
                
                <!-- Location -->
                <div class="flex items-center text-sm text-gray-500 mb-3">
                    <svg class="h-4 w-4 mr-1" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                        <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M17.657 16.657L13.414 20.9a1.998 1.998 0 01-2.827 0l-4.244-4.243a8 8 0 1111.314 0z" />
                        <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M15 11a3 3 0 11-6 0 3 3 0 016 0z" />
                    </svg>
                    {{ listing.location }}
                </div>
                
                <!-- Property Info -->
                <div class="flex items-center justify-between mb-4">
                    <div class="flex items-center text-sm text-gray-500">
                        <svg class="h-4 w-4 mr-1" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M4 8V6a2 2 0 012-2h2M4 8v8a2 2 0 002 2h8a2 2 0 002-2V8M4 8h16M8 2v4m8-4v4" />
                        </svg>
                        {{ listing.size_acres }} acres
                    </div>
                    <div class="text-lg font-bold text-primary-600">
                        ${{ listing.price|floatformat:0 }}
                    </div>
                </div>
                
                <!-- Action Buttons -->
                <div class="flex space-x-2">
                    <a href="/listings/{{ listing.id }}/" 
                       class="flex-1 bg-primary-600 text-white text-center py-2 px-4 rounded-md text-sm font-medium hover:bg-primary-700 transition-colors duration-200">
                        View Details
                    </a>
                    {% if user.is_authenticated and user.profile.role == 'buyer' %}
                        <button class="px-4 py-2 border border-gray-300 rounded-md text-sm font-medium text-gray-700 hover:bg-gray-50 transition-colors duration-200"
                                hx-get="/api/inquiry-form/{{ listing.id }}/"
                                hx-target="#inquiry-modal"
                                hx-trigger="click">
                            <svg class="h-4 w-4" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M3 8l7.89 5.26a2 2 0 002.22 0L21 8M5 19h14a2 2 0 002-2V7a2 2 0 00-2-2H5a2 2 0 00-2 2v10a2 2 0 002 2z" />
                            </svg>
                        </button>
                    {% endif %}
                </div>
            </div>
        </div>
    {% empty %}
        <!-- No listings available -->
        <div class="col-span-full text-center py-12">
            <svg class="mx-auto h-12 w-12 text-gray-400" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M19 21V5a2 2 0 00-2-2H7a2 2 0 00-2 2v16m14 0h2m-2 0h-5m-9 0H3m2 0h5M9 7h1m-1 4h1m4-4h1m-1 4h1m-5 10v-5a1 1 0 011-1h2a1 1 0 011 1v5m-4 0h4" />
            </svg>
            <h3 class="mt-2 text-sm font-medium text-gray-900">No featured listings</h3>
            <p class="mt-1 text-sm text-gray-500">Check back soon for new property listings.</p>
        </div>
    {% endfor %}
</div>

<!-- Inquiry Modal Container -->
<div id="inquiry-modal"></div>

<style>
    /* Line clamp utility classes */
    .line-clamp-2 {
        display: -webkit-box;
        -webkit-line-clamp: 2;
        -webkit-box-orient: vertical;
        overflow: hidden;
    }
</style>
