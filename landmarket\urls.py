from django.urls import path
from . import views
from django.contrib.auth import views as auth_views

urlpatterns = [
    # Public pages
    path('', views.landing, name='landing'),

    # Authentication
    path('login/', auth_views.LoginView.as_view(template_name='auth/login.html'), name='login'),
    path('logout/', auth_views.LogoutView.as_view(next_page='/'), name='logout'),
    path('register/', views.register, name='register'),

    # Dashboards
    path('dashboard/', views.dashboard, name='dashboard'),

    # API endpoints for HTMX
    path('api/featured-listings/', views.api_featured_listings, name='api_featured_listings'),

    # Admin API endpoints
    path('admin/api/recent-activity/', views.admin_api_recent_activity, name='admin_api_recent_activity'),
    path('admin/api/approve-listing/<int:listing_id>/', views.admin_api_approve_listing, name='admin_api_approve_listing'),
    path('admin/api/reject-listing/<int:listing_id>/', views.admin_api_reject_listing, name='admin_api_reject_listing'),
]