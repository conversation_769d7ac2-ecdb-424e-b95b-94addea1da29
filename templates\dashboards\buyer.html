{% extends 'base.html' %}
{% load static %}

{% block title %}Buyer Dashboard - LandHub{% endblock %}
{% block meta_description %}LandHub Buyer Dashboard - Discover properties, manage favorites, and track your land search journey{% endblock %}

{% block extra_css %}
<style>
    /* Custom buyer dashboard styles - consistent with admin/seller dashboards */
    .admin-card {
        background: linear-gradient(135deg, rgba(255,255,255,0.9) 0%, rgba(255,255,255,0.95) 100%);
        backdrop-filter: blur(10px);
        border: 1px solid rgba(255,255,255,0.2);
    }
    
    .metric-card {
        transition: all 0.3s ease;
    }
    
    .metric-card:hover {
        transform: translateY(-2px);
        box-shadow: 0 10px 25px rgba(0,0,0,0.1);
    }
    
    .chart-bar {
        transition: all 0.3s ease;
    }
    
    .chart-bar:hover {
        opacity: 0.8;
        transform: scaleY(1.05);
    }
    
    .property-card {
        transition: all 0.3s ease;
    }
    
    .property-card:hover {
        transform: translateY(-1px);
        box-shadow: 0 8px 20px rgba(0,0,0,0.08);
    }
</style>
{% endblock %}

{% block content %}
<!-- Buyer Dashboard Content -->
<div class="bg-gradient-to-br from-gray-50 to-gray-100 min-h-screen" x-data="buyerDashboard()">
    <!-- Dashboard Header -->
    <div class="bg-white border-b border-gray-200 shadow-sm">
        <div class="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
            <div class="flex items-center justify-between py-6">
                <div>
                    <h1 class="text-2xl font-bold text-gray-900">Buyer Dashboard</h1>
                    <p class="mt-1 text-sm text-gray-600">Welcome back, {{ user.get_full_name|default:user.username }}. Discover your perfect land investment today.</p>
                </div>
                <div class="flex items-center space-x-3">
                    <!-- Refresh Button -->
                    <button @click="refreshData()" 
                            :disabled="loading"
                            class="inline-flex items-center px-4 py-2 border border-gray-300 rounded-lg text-sm font-medium text-gray-700 bg-white hover:bg-gray-50 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-primary-500 disabled:opacity-50 transition-all duration-200">
                        <svg class="w-4 h-4 mr-2" :class="{ 'animate-spin': loading }" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M4 4v5h.582m15.356 2A8.001 8.001 0 004.582 9m0 0H9m11 11v-5h-.581m0 0a8.003 8.003 0 01-15.357-2m15.357 2H15" />
                        </svg>
                        Refresh
                    </button>
                    <!-- Quick Actions Dropdown -->
                    <div class="relative" x-data="{ open: false }">
                        <button @click="open = !open" 
                                class="inline-flex items-center px-4 py-2 bg-primary-600 text-white rounded-lg text-sm font-medium hover:bg-primary-700 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-primary-500 transition-all duration-200 shadow-sm">
                            Quick Actions
                            <svg class="w-4 h-4 ml-2" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M19 9l-7 7-7-7" />
                            </svg>
                        </button>
                        <div x-show="open" 
                             x-transition:enter="transition ease-out duration-100"
                             x-transition:enter-start="transform opacity-0 scale-95"
                             x-transition:enter-end="transform opacity-100 scale-100"
                             x-transition:leave="transition ease-in duration-75"
                             x-transition:leave-start="transform opacity-100 scale-100"
                             x-transition:leave-end="transform opacity-0 scale-95"
                             @click.away="open = false"
                             class="absolute right-0 mt-2 w-56 bg-white rounded-lg shadow-lg border border-gray-200 z-10">
                            <div class="py-1">
                                <a href="#" class="block px-4 py-2 text-sm text-gray-700 hover:bg-gray-100 transition-colors duration-150">Browse Listings</a>
                                <a href="#" class="block px-4 py-2 text-sm text-gray-700 hover:bg-gray-100 transition-colors duration-150">Create Search Alert</a>
                                <a href="#" class="block px-4 py-2 text-sm text-gray-700 hover:bg-gray-100 transition-colors duration-150">View Favorites</a>
                                <div class="border-t border-gray-100"></div>
                                <a href="#" class="block px-4 py-2 text-sm text-gray-700 hover:bg-gray-100 transition-colors duration-150">Account Settings</a>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>

    <!-- Main Dashboard Content -->
    <div class="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8 py-8">
        <!-- Key Metrics Cards -->
        <div class="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-6 mb-8">
            <!-- Saved Searches Card -->
            <div class="admin-card metric-card rounded-xl shadow-sm p-6">
                <div class="flex items-center">
                    <div class="flex-shrink-0">
                        <div class="w-12 h-12 bg-gradient-to-br from-blue-500 to-blue-600 rounded-xl flex items-center justify-center shadow-lg">
                            <svg class="w-6 h-6 text-white" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M21 21l-6-6m2-5a7 7 0 11-14 0 7 7 0 0114 0z" />
                            </svg>
                        </div>
                    </div>
                    <div class="ml-4 flex-1">
                        <p class="text-sm font-medium text-gray-600">Saved Searches</p>
                        <div class="flex items-baseline">
                            <p class="text-2xl font-bold text-gray-900">{{ saved_searches|default:0 }}</p>
                            <p class="ml-2 text-sm font-medium text-green-600 bg-green-50 px-2 py-1 rounded-full">+2 this week</p>
                        </div>
                    </div>
                </div>
                <div class="mt-4 pt-4 border-t border-gray-100">
                    <div class="flex items-center justify-between text-sm text-gray-500">
                        <span>Active: {{ active_searches|default:0 }}</span>
                        <span>Alerts: {{ search_alerts|default:0 }}</span>
                    </div>
                </div>
            </div>

            <!-- Favorite Properties Card -->
            <div class="admin-card metric-card rounded-xl shadow-sm p-6">
                <div class="flex items-center">
                    <div class="flex-shrink-0">
                        <div class="w-12 h-12 bg-gradient-to-br from-red-500 to-pink-600 rounded-xl flex items-center justify-center shadow-lg">
                            <svg class="w-6 h-6 text-white" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M4.318 6.318a4.5 4.5 0 000 6.364L12 20.364l7.682-7.682a4.5 4.5 0 00-6.364-6.364L12 7.636l-1.318-1.318a4.5 4.5 0 00-6.364 0z" />
                            </svg>
                        </div>
                    </div>
                    <div class="ml-4 flex-1">
                        <p class="text-sm font-medium text-gray-600">Favorite Properties</p>
                        <div class="flex items-baseline">
                            <p class="text-2xl font-bold text-gray-900">{{ favorite_properties|default:0 }}</p>
                            <p class="ml-2 text-sm font-medium text-blue-600 bg-blue-50 px-2 py-1 rounded-full">+5 recent</p>
                        </div>
                    </div>
                </div>
                <div class="mt-4 pt-4 border-t border-gray-100">
                    <div class="flex items-center text-sm text-gray-500">
                        <span>Avg. price: ${{ avg_favorite_price|default:0|floatformat:0 }}</span>
                    </div>
                </div>
            </div>

            <!-- Inquiries Sent Card -->
            <div class="admin-card metric-card rounded-xl shadow-sm p-6">
                <div class="flex items-center">
                    <div class="flex-shrink-0">
                        <div class="w-12 h-12 bg-gradient-to-br from-green-500 to-emerald-600 rounded-xl flex items-center justify-center shadow-lg">
                            <svg class="w-6 h-6 text-white" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M8 12h.01M12 12h.01M16 12h.01M21 12c0 4.418-4.03 8-9 8a9.863 9.863 0 01-4.255-.949L3 20l1.395-3.72C3.512 15.042 3 13.574 3 12c0-4.418 4.03-8 9-8s9 3.582 9 8z" />
                            </svg>
                        </div>
                    </div>
                    <div class="ml-4 flex-1">
                        <p class="text-sm font-medium text-gray-600">Inquiries Sent</p>
                        <div class="flex items-baseline">
                            <p class="text-2xl font-bold text-gray-900">{{ inquiries_sent|default:0 }}</p>
                            {% if pending_responses > 0 %}
                                <span class="ml-2 inline-flex items-center px-2.5 py-0.5 rounded-full text-xs font-medium bg-yellow-100 text-yellow-800">
                                    {{ pending_responses }} pending
                                </span>
                            {% endif %}
                        </div>
                    </div>
                </div>
                <div class="mt-4 pt-4 border-t border-gray-100">
                    <div class="flex items-center text-sm text-gray-500">
                        <span>Response rate: {{ response_rate|default:0 }}%</span>
                    </div>
                </div>
            </div>

            <!-- Properties Viewed Card -->
            <div class="admin-card metric-card rounded-xl shadow-sm p-6">
                <div class="flex items-center">
                    <div class="flex-shrink-0">
                        <div class="w-12 h-12 bg-gradient-to-br from-purple-500 to-indigo-600 rounded-xl flex items-center justify-center shadow-lg">
                            <svg class="w-6 h-6 text-white" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M15 12a3 3 0 11-6 0 3 3 0 016 0z" />
                                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M2.458 12C3.732 7.943 7.523 5 12 5c4.478 0 8.268 2.943 9.542 7-1.274 4.057-5.064 7-9.542 7-4.477 0-8.268-2.943-9.542-7z" />
                            </svg>
                        </div>
                    </div>
                    <div class="ml-4 flex-1">
                        <p class="text-sm font-medium text-gray-600">Properties Viewed</p>
                        <div class="flex items-baseline">
                            <p class="text-2xl font-bold text-gray-900">{{ properties_viewed|default:0 }}</p>
                            <p class="ml-2 text-sm font-medium text-green-600 bg-green-50 px-2 py-1 rounded-full">+18%</p>
                        </div>
                    </div>
                </div>
                <div class="mt-4 pt-4 border-t border-gray-100">
                    <div class="flex items-center text-sm text-gray-500">
                        <span>This month: {{ monthly_views|default:0 }}</span>
                    </div>
                </div>
            </div>
        </div>

        <!-- Charts and Analytics Section -->
        <div class="grid grid-cols-1 lg:grid-cols-2 gap-8 mb-8">
            <!-- Search Activity Chart -->
            <div class="admin-card rounded-xl shadow-sm p-6">
                <div class="flex items-center justify-between mb-6">
                    <h3 class="text-lg font-semibold text-gray-900">Search Activity</h3>
                    <div class="flex items-center space-x-1 bg-gray-100 rounded-lg p-1">
                        <button class="text-sm text-gray-500 hover:text-gray-700 px-3 py-1 rounded-md hover:bg-white transition-all duration-150">7D</button>
                        <button class="text-sm text-white bg-primary-600 px-3 py-1 rounded-md shadow-sm">30D</button>
                        <button class="text-sm text-gray-500 hover:text-gray-700 px-3 py-1 rounded-md hover:bg-white transition-all duration-150">90D</button>
                    </div>
                </div>
                <div class="h-64 flex items-end justify-between space-x-2">
                    <!-- Enhanced Bar Chart for Search Activity -->
                    <div class="chart-bar flex-1 bg-gradient-to-t from-purple-500 to-purple-400 rounded-t-lg shadow-sm" style="height: 35%"></div>
                    <div class="chart-bar flex-1 bg-gradient-to-t from-purple-500 to-purple-400 rounded-t-lg shadow-sm" style="height: 55%"></div>
                    <div class="chart-bar flex-1 bg-gradient-to-t from-purple-600 to-purple-500 rounded-t-lg shadow-sm" style="height: 75%"></div>
                    <div class="chart-bar flex-1 bg-gradient-to-t from-purple-500 to-purple-400 rounded-t-lg shadow-sm" style="height: 65%"></div>
                    <div class="chart-bar flex-1 bg-gradient-to-t from-purple-600 to-purple-500 rounded-t-lg shadow-sm" style="height: 85%"></div>
                    <div class="chart-bar flex-1 bg-gradient-to-t from-purple-700 to-purple-600 rounded-t-lg shadow-sm" style="height: 100%"></div>
                    <div class="chart-bar flex-1 bg-gradient-to-t from-purple-600 to-purple-500 rounded-t-lg shadow-sm" style="height: 80%"></div>
                </div>
                <div class="flex justify-between text-xs text-gray-500 mt-4 px-1">
                    <span>Week 1</span>
                    <span>Week 2</span>
                    <span>Week 3</span>
                    <span>Week 4</span>
                </div>
            </div>

            <!-- Preferred Property Types -->
            <div class="admin-card rounded-xl shadow-sm p-6">
                <h3 class="text-lg font-semibold text-gray-900 mb-6">Preferred Property Types</h3>
                <div class="space-y-5">
                    <div class="flex items-center justify-between">
                        <div class="flex items-center">
                            <div class="w-4 h-4 bg-gradient-to-r from-green-500 to-emerald-600 rounded-full mr-3 shadow-sm"></div>
                            <span class="text-sm font-medium text-gray-700">Agricultural</span>
                        </div>
                        <div class="flex items-center">
                            <div class="w-32 bg-gray-200 rounded-full h-3 mr-3 overflow-hidden">
                                <div class="bg-gradient-to-r from-green-500 to-emerald-600 h-3 rounded-full transition-all duration-500 shadow-sm" style="width: 40%"></div>
                            </div>
                            <span class="text-sm font-bold text-gray-900 min-w-[3rem] text-right">40%</span>
                        </div>
                    </div>
                    <div class="flex items-center justify-between">
                        <div class="flex items-center">
                            <div class="w-4 h-4 bg-gradient-to-r from-blue-500 to-blue-600 rounded-full mr-3 shadow-sm"></div>
                            <span class="text-sm font-medium text-gray-700">Residential</span>
                        </div>
                        <div class="flex items-center">
                            <div class="w-32 bg-gray-200 rounded-full h-3 mr-3 overflow-hidden">
                                <div class="bg-gradient-to-r from-blue-500 to-blue-600 h-3 rounded-full transition-all duration-500 shadow-sm" style="width: 35%"></div>
                            </div>
                            <span class="text-sm font-bold text-gray-900 min-w-[3rem] text-right">35%</span>
                        </div>
                    </div>
                    <div class="flex items-center justify-between">
                        <div class="flex items-center">
                            <div class="w-4 h-4 bg-gradient-to-r from-orange-500 to-red-500 rounded-full mr-3 shadow-sm"></div>
                            <span class="text-sm font-medium text-gray-700">Recreational</span>
                        </div>
                        <div class="flex items-center">
                            <div class="w-32 bg-gray-200 rounded-full h-3 mr-3 overflow-hidden">
                                <div class="bg-gradient-to-r from-orange-500 to-red-500 h-3 rounded-full transition-all duration-500 shadow-sm" style="width: 20%"></div>
                            </div>
                            <span class="text-sm font-bold text-gray-900 min-w-[3rem] text-right">20%</span>
                        </div>
                    </div>
                    <div class="flex items-center justify-between">
                        <div class="flex items-center">
                            <div class="w-4 h-4 bg-gradient-to-r from-purple-500 to-indigo-600 rounded-full mr-3 shadow-sm"></div>
                            <span class="text-sm font-medium text-gray-700">Commercial</span>
                        </div>
                        <div class="flex items-center">
                            <div class="w-32 bg-gray-200 rounded-full h-3 mr-3 overflow-hidden">
                                <div class="bg-gradient-to-r from-purple-500 to-indigo-600 h-3 rounded-full transition-all duration-500 shadow-sm" style="width: 5%"></div>
                            </div>
                            <span class="text-sm font-bold text-gray-900 min-w-[3rem] text-right">5%</span>
                        </div>
                    </div>
                </div>
            </div>
        </div>

        <!-- Recent Activity and Management Section -->
        <div class="grid grid-cols-1 lg:grid-cols-3 gap-8">
            <!-- Recent Activity Feed -->
            <div class="lg:col-span-2 admin-card rounded-xl shadow-sm">
                <div class="px-6 py-4 border-b border-gray-100">
                    <div class="flex items-center justify-between">
                        <h3 class="text-lg font-semibold text-gray-900">Recent Activity</h3>
                        <button onclick="window.location.reload()"
                                class="text-sm text-primary-600 hover:text-primary-500 font-medium bg-primary-50 px-3 py-1 rounded-lg hover:bg-primary-100 transition-all duration-150">
                            <svg class="w-4 h-4 inline mr-1" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M4 4v5h.582m15.356 2A8.001 8.001 0 004.582 9m0 0H9m11 11v-5h-.581m0 0a8.003 8.003 0 01-15.357-2m15.357 2H15" />
                            </svg>
                            Refresh
                        </button>
                    </div>
                </div>

                <div id="activity-feed" class="p-6">
                    <!-- Static Activity Items -->
                    <div class="space-y-4">
                        <div class="flex items-start space-x-3 p-4 bg-red-50 rounded-lg">
                            <div class="flex-shrink-0">
                                <div class="w-8 h-8 bg-red-500 rounded-full flex items-center justify-center">
                                    <svg class="w-4 h-4 text-white" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                                        <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M4.318 6.318a4.5 4.5 0 000 6.364L12 20.364l7.682-7.682a4.5 4.5 0 00-6.364-6.364L12 7.636l-1.318-1.318a4.5 4.5 0 00-6.364 0z" />
                                    </svg>
                                </div>
                            </div>
                            <div class="flex-1 min-w-0">
                                <p class="text-sm font-medium text-gray-900">Added to favorites</p>
                                <p class="text-sm text-gray-500">50-acre agricultural property in Texas saved to your favorites</p>
                                <p class="text-xs text-gray-400 mt-1">1 hour ago</p>
                            </div>
                        </div>

                        <div class="flex items-start space-x-3 p-4 bg-blue-50 rounded-lg">
                            <div class="flex-shrink-0">
                                <div class="w-8 h-8 bg-blue-500 rounded-full flex items-center justify-center">
                                    <svg class="w-4 h-4 text-white" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                                        <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M21 21l-6-6m2-5a7 7 0 11-14 0 7 7 0 0114 0z" />
                                    </svg>
                                </div>
                            </div>
                            <div class="flex-1 min-w-0">
                                <p class="text-sm font-medium text-gray-900">New search created</p>
                                <p class="text-sm text-gray-500">Agricultural land under $200k in Texas with email alerts enabled</p>
                                <p class="text-xs text-gray-400 mt-1">3 hours ago</p>
                            </div>
                        </div>

                        <div class="flex items-start space-x-3 p-4 bg-green-50 rounded-lg">
                            <div class="flex-shrink-0">
                                <div class="w-8 h-8 bg-green-500 rounded-full flex items-center justify-center">
                                    <svg class="w-4 h-4 text-white" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                                        <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M3 8l7.89 5.26a2 2 0 002.22 0L21 8M5 19h14a2 2 0 002-2V7a2 2 0 00-2-2H5a2 2 0 00-2 2v10a2 2 0 002 2z" />
                                    </svg>
                                </div>
                            </div>
                            <div class="flex-1 min-w-0">
                                <p class="text-sm font-medium text-gray-900">Inquiry response received</p>
                                <p class="text-sm text-gray-500">Sarah Johnson responded to your inquiry about Mountain View Ranch</p>
                                <p class="text-xs text-gray-400 mt-1">5 hours ago</p>
                            </div>
                        </div>

                        <div class="flex items-start space-x-3 p-4 bg-purple-50 rounded-lg">
                            <div class="flex-shrink-0">
                                <div class="w-8 h-8 bg-purple-500 rounded-full flex items-center justify-center">
                                    <svg class="w-4 h-4 text-white" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                                        <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M15 12a3 3 0 11-6 0 3 3 0 016 0z" />
                                        <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M2.458 12C3.732 7.943 7.523 5 12 5c4.478 0 8.268 2.943 9.542 7-1.274 4.057-5.064 7-9.542 7-4.477 0-8.268-2.943-9.542-7z" />
                                    </svg>
                                </div>
                            </div>
                            <div class="flex-1 min-w-0">
                                <p class="text-sm font-medium text-gray-900">Property viewed</p>
                                <p class="text-sm text-gray-500">You viewed Residential Development Land in California</p>
                                <p class="text-xs text-gray-400 mt-1">1 day ago</p>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
