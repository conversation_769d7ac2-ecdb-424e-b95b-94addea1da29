from django.shortcuts import render, redirect
from django.contrib.auth.decorators import login_required
from django.contrib.auth.forms import UserCreationForm
from django.contrib.auth import login
from django.http import JsonResponse, HttpResponseForbidden
from django.contrib.auth.models import User
from django.db.models import Count, Q
from django.utils import timezone
from datetime import datetime, timedelta
from .models import Land, UserProfile, Inquiry


def register(request):
    """User registration view"""
    if request.method == 'POST':
        form = UserCreationForm(request.POST)
        if form.is_valid():
            user = form.save()
            # Set user role based on URL parameter
            role = request.GET.get('role', 'buyer')
            if role in ['admin', 'seller', 'buyer']:
                user.profile.role = role
                user.profile.save()
            login(request, user)
            return redirect('dashboard')
    else:
        form = UserCreationForm()

    context = {
        'form': form,
        'role': request.GET.get('role', 'buyer')
    }
    return render(request, 'auth/register.html', context)


def landing(request):
    """Landing page view"""
    # Get some featured listings for the landing page
    featured_listings = Land.objects.filter(
        status='approved',
        is_approved=True
    ).order_by('-created_at')[:6]

    context = {
        'featured_listings': featured_listings,
    }
    return render(request, 'landing.html', context)


@login_required
def dashboard(request):
    """Role-based dashboard redirect"""
    user_role = request.user.profile.role

    if user_role == 'admin':
        return admin_dashboard(request)
    elif user_role == 'seller':
        return seller_dashboard(request)
    elif user_role == 'buyer':
        return buyer_dashboard(request)
    else:
        return render(request, 'landing.html')


@login_required
def admin_dashboard(request):
    """Admin dashboard view with comprehensive analytics"""
    # Role-based access control
    if not hasattr(request.user, 'profile') or request.user.profile.role != 'admin':
        return redirect('landing')

    # Calculate date ranges for analytics
    today = timezone.now().date()
    thirty_days_ago = today - timedelta(days=30)

    # User statistics
    total_users = User.objects.count()
    buyer_count = UserProfile.objects.filter(role='buyer').count()
    seller_count = UserProfile.objects.filter(role='seller').count()
    admin_count = UserProfile.objects.filter(role='admin').count()
    daily_new_users = User.objects.filter(date_joined__date=today).count()

    # Listing statistics
    total_listings = Land.objects.count()
    pending_listings = Land.objects.filter(status='pending').count()
    approved_listings = Land.objects.filter(status='approved', is_approved=True).count()
    draft_listings = Land.objects.filter(status='draft').count()
    rejected_listings = Land.objects.filter(status='rejected').count()
    daily_new_listings = Land.objects.filter(created_at__date=today).count()

    # Inquiry statistics
    total_inquiries = Inquiry.objects.count()
    monthly_inquiries = Inquiry.objects.filter(created_at__date__gte=thirty_days_ago).count()
    daily_inquiries = Inquiry.objects.filter(created_at__date=today).count()

    # Recent pending listings for quick review
    recent_pending_listings = Land.objects.filter(
        status='pending'
    ).select_related('owner').order_by('-created_at')[:5]

    # Property type distribution
    property_type_stats = Land.objects.filter(
        status='approved', is_approved=True
    ).values('property_type').annotate(count=Count('id'))

    # Calculate percentages for property types
    total_approved = approved_listings
    property_distribution = {}
    for stat in property_type_stats:
        property_distribution[stat['property_type']] = {
            'count': stat['count'],
            'percentage': round((stat['count'] / total_approved * 100) if total_approved > 0 else 0, 1)
        }

    context = {
        # User metrics
        'total_users': total_users,
        'buyer_count': buyer_count,
        'seller_count': seller_count,
        'admin_count': admin_count,
        'daily_new_users': daily_new_users,

        # Listing metrics
        'total_listings': total_listings,
        'pending_listings': pending_listings,
        'approved_listings': approved_listings,
        'draft_listings': draft_listings,
        'rejected_listings': rejected_listings,
        'daily_new_listings': daily_new_listings,

        # Inquiry metrics
        'total_inquiries': total_inquiries,
        'monthly_inquiries': monthly_inquiries,
        'daily_inquiries': daily_inquiries,

        # Additional data
        'recent_pending_listings': recent_pending_listings,
        'property_distribution': property_distribution,
        'daily_page_views': 1250,  # This would come from analytics service
    }

    return render(request, 'dashboards/admin.html', context)


@login_required
def seller_dashboard(request):
    """Seller dashboard view with comprehensive seller analytics"""
    # Role-based access control
    if not hasattr(request.user, 'profile') or request.user.profile.role != 'seller':
        return redirect('landing')

    # Calculate date ranges for analytics
    today = timezone.now().date()
    thirty_days_ago = today - timedelta(days=30)

    # Get user's listings
    user_listings = Land.objects.filter(owner=request.user)

    # Listing statistics
    total_listings = user_listings.count()
    active_listings = user_listings.filter(status='approved').count()
    draft_listings = user_listings.filter(status='draft').count()
    pending_listings = user_listings.filter(status='pending').count()

    # Calculate views (mock data for now - would come from analytics system)
    total_views = total_listings * 45  # Mock calculation
    monthly_views = total_listings * 15  # Mock calculation
    daily_views = 8  # Mock data

    # Calculate inquiries (mock data - would come from inquiry system)
    total_inquiries = total_listings * 3  # Mock calculation
    pending_inquiries = 2  # Mock data
    daily_inquiries = 1  # Mock data
    response_rate = 85  # Mock percentage

    # Calculate pricing statistics
    if user_listings.exists():
        prices = [listing.price for listing in user_listings if listing.price]
        average_price = sum(prices) / len(prices) if prices else 0

        # Calculate price per acre
        total_acres = sum([listing.size_acres for listing in user_listings if listing.size_acres])
        price_per_acre = average_price / (total_acres / total_listings) if total_acres > 0 else 0
    else:
        average_price = 0
        price_per_acre = 0

    # Mock data for additional metrics
    daily_profile_views = 12

    context = {
        'user_listings': user_listings,
        'total_listings': total_listings,
        'active_listings': active_listings,
        'draft_listings': draft_listings,
        'pending_listings': pending_listings,
        'total_views': total_views,
        'monthly_views': monthly_views,
        'daily_views': daily_views,
        'total_inquiries': total_inquiries,
        'pending_inquiries': pending_inquiries,
        'daily_inquiries': daily_inquiries,
        'response_rate': response_rate,
        'average_price': average_price,
        'price_per_acre': price_per_acre,
        'daily_profile_views': daily_profile_views,
    }
    return render(request, 'dashboards/seller.html', context)


@login_required
def buyer_dashboard(request):
    """Buyer dashboard view"""
    if request.user.profile.role != 'buyer':
        return render(request, 'landing.html')

    # Buyer dashboard logic here
    context = {
        'favorites_count': request.user.favorites.count(),
        'inquiries_count': request.user.inquiries_sent.count(),
        'saved_searches_count': request.user.saved_searches.count(),
    }
    return render(request, 'dashboards/buyer.html', context)


def api_featured_listings(request):
    """API endpoint for featured listings (HTMX)"""
    featured_listings = Land.objects.filter(
        status='approved',
        is_approved=True
    ).order_by('-created_at')[:6]

    context = {
        'featured_listings': featured_listings,
    }
    return render(request, 'components/featured_listings.html', context)


@login_required
def admin_api_recent_activity(request):
    """API endpoint for recent activity feed (HTMX)"""
    if not hasattr(request.user, 'profile') or request.user.profile.role != 'admin':
        return JsonResponse({'error': 'Unauthorized'}, status=403)

    # Get recent activities from different models
    recent_users = User.objects.order_by('-date_joined')[:3]
    recent_listings = Land.objects.order_by('-created_at')[:3]
    recent_inquiries = Inquiry.objects.select_related('buyer', 'land').order_by('-created_at')[:3]

    context = {
        'recent_users': recent_users,
        'recent_listings': recent_listings,
        'recent_inquiries': recent_inquiries,
    }
    return render(request, 'components/admin_activity_feed.html', context)


@login_required
def admin_api_approve_listing(request, listing_id):
    """API endpoint to approve a listing (HTMX)"""
    if not hasattr(request.user, 'profile') or request.user.profile.role != 'admin':
        return JsonResponse({'error': 'Unauthorized'}, status=403)

    if request.method == 'POST':
        try:
            listing = Land.objects.get(id=listing_id)
            listing.status = 'approved'
            listing.is_approved = True
            listing.save()

            return render(request, 'components/admin_listing_approved.html', {
                'listing': listing,
                'message': 'Listing approved successfully'
            })
        except Land.DoesNotExist:
            return JsonResponse({'error': 'Listing not found'}, status=404)

    return JsonResponse({'error': 'Method not allowed'}, status=405)


@login_required
def admin_api_reject_listing(request, listing_id):
    """API endpoint to reject a listing (HTMX)"""
    if not hasattr(request.user, 'profile') or request.user.profile.role != 'admin':
        return JsonResponse({'error': 'Unauthorized'}, status=403)

    if request.method == 'POST':
        try:
            listing = Land.objects.get(id=listing_id)
            listing.status = 'rejected'
            listing.is_approved = False
            listing.admin_notes = 'Rejected by admin'
            listing.save()

            return render(request, 'components/admin_listing_rejected.html', {
                'listing': listing,
                'message': 'Listing rejected'
            })
        except Land.DoesNotExist:
            return JsonResponse({'error': 'Listing not found'}, status=404)

    return JsonResponse({'error': 'Method not allowed'}, status=405)