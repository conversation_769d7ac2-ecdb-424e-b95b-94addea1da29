{% load static %}
<!DOCTYPE html>
<html lang="en" class="h-full">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <meta name="description" content="{% block meta_description %}LandHub - Your premier land-only real estate platform{% endblock %}">
    <meta name="keywords" content="{% block meta_keywords %}land, real estate, property, buy land, sell land{% endblock %}">
    <meta name="author" content="LandHub">
    
    <!-- Mobile optimization -->
    <meta name="format-detection" content="telephone=no">
    <meta name="mobile-web-app-capable" content="yes">
    <meta name="apple-mobile-web-app-capable" content="yes">
    <meta name="apple-mobile-web-app-status-bar-style" content="default">
    <meta name="theme-color" content="#059669">
    
    <title>{% block title %}LandHub - Land Real Estate Platform{% endblock %}</title>
    
    <!-- Favicon -->
    <link rel="icon" type="image/x-icon" href="{% static 'favicon.ico' %}">
    <link rel="apple-touch-icon" href="{% static 'apple-touch-icon.png' %}">
    
    <!-- Tailwind CSS via CDN -->
    <script src="https://cdn.tailwindcss.com"></script>
    <script>
        tailwind.config = {
            theme: {
                extend: {
                    colors: {
                        primary: {
                            50: '#ecfdf5',
                            100: '#d1fae5',
                            200: '#a7f3d0',
                            300: '#6ee7b7',
                            400: '#34d399',
                            500: '#10b981',
                            600: '#059669',
                            700: '#047857',
                            800: '#065f46',
                            900: '#064e3b',
                        }
                    },
                    fontFamily: {
                        'sans': ['Inter', 'system-ui', 'sans-serif'],
                    }
                }
            }
        }
    </script>
    
    <!-- Google Fonts -->
    <link rel="preconnect" href="https://fonts.googleapis.com">
    <link rel="preconnect" href="https://fonts.gstatic.com" crossorigin>
    <link href="https://fonts.googleapis.com/css2?family=Inter:wght@300;400;500;600;700&display=swap" rel="stylesheet">
    
    <!-- HTMX -->
    <script src="https://unpkg.com/htmx.org@1.9.10/dist/htmx.min.js"></script>
    
    <!-- Unpoly -->
    <script src="https://unpkg.com/unpoly@3.7.2/unpoly.min.js"></script>
    <link rel="stylesheet" href="https://unpkg.com/unpoly@3.7.2/unpoly.min.css">
    
    <!-- Alpine.js -->
    <script defer src="https://unpkg.com/alpinejs@3.13.5/dist/cdn.min.js"></script>
    
    <!-- Custom CSS -->
    <style>
        /* Custom scrollbar */
        ::-webkit-scrollbar {
            width: 6px;
        }
        ::-webkit-scrollbar-track {
            background: #f1f5f9;
        }
        ::-webkit-scrollbar-thumb {
            background: #cbd5e1;
            border-radius: 3px;
        }
        ::-webkit-scrollbar-thumb:hover {
            background: #94a3b8;
        }
        
        /* Loading states */
        .htmx-request {
            opacity: 0.6;
            transition: opacity 0.2s ease;
        }
        
        /* Mobile touch improvements */
        @media (max-width: 768px) {
            input, textarea, select {
                font-size: 16px; /* Prevents zoom on iOS */
            }
        }
        
        /* Smooth transitions */
        * {
            transition-property: color, background-color, border-color, text-decoration-color, fill, stroke, opacity, box-shadow, transform, filter, backdrop-filter;
            transition-timing-function: cubic-bezier(0.4, 0, 0.2, 1);
            transition-duration: 150ms;
        }
    </style>
    
    {% block extra_css %}{% endblock %}
</head>
<body class="h-full bg-gray-50 font-sans antialiased" x-data="{ sidebarOpen: false }">
    <!-- Loading indicator -->
    <div id="loading-indicator" class="fixed top-0 left-0 right-0 z-50 h-1 bg-primary-500 transform scale-x-0 origin-left transition-transform duration-300 htmx-indicator"></div>
    
    <!-- Mobile sidebar overlay -->
    <div x-show="sidebarOpen" 
         x-transition:enter="transition-opacity ease-linear duration-300"
         x-transition:enter-start="opacity-0"
         x-transition:enter-end="opacity-100"
         x-transition:leave="transition-opacity ease-linear duration-300"
         x-transition:leave-start="opacity-100"
         x-transition:leave-end="opacity-0"
         class="fixed inset-0 z-40 lg:hidden"
         @click="sidebarOpen = false">
        <div class="absolute inset-0 bg-gray-600 opacity-75"></div>
    </div>
    
    <!-- Main container -->
    <div class="flex h-full">
        <!-- Sidebar -->
        {% if user.is_authenticated %}
            {% include 'components/sidebar.html' %}
        {% endif %}

        <!-- Main content area -->
        <div class="flex-1 flex flex-col {% if user.is_authenticated %}lg:ml-64{% endif %} min-h-screen">
            <!-- Top navigation bar -->
            <header class="bg-white shadow-sm border-b border-gray-200 sticky top-0 z-30">
                <div class="flex items-center justify-between px-4 py-3 sm:px-6 lg:px-8">
                    <!-- Mobile menu button and Logo for non-authenticated users -->
                    {% if user.is_authenticated %}
                        <button @click="sidebarOpen = !sidebarOpen"
                                class="lg:hidden p-2 rounded-md text-gray-400 hover:text-gray-500 hover:bg-gray-100 focus:outline-none focus:ring-2 focus:ring-inset focus:ring-primary-500">
                            <span class="sr-only">Open sidebar</span>
                            <svg class="h-6 w-6" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M4 6h16M4 12h16M4 18h16" />
                            </svg>
                        </button>
                        <!-- Empty div for spacing when authenticated (logo is in sidebar) -->
                        <div class="hidden lg:block"></div>
                    {% else %}
                        <!-- Logo for non-authenticated users -->
                        <div class="flex items-center">
                            <a href="{% url 'landing' %}" class="flex items-center space-x-2">
                                <div class="w-8 h-8 bg-primary-600 rounded-lg flex items-center justify-center">
                                    <span class="text-white font-bold text-lg">L</span>
                                </div>
                                <span class="text-xl font-bold text-gray-900">LandHub</span>
                            </a>
                        </div>
                    {% endif %}
                    
                    <!-- User menu -->
                    <div class="flex items-center space-x-4">
                        {% if user.is_authenticated %}
                            <!-- Notifications -->
                            <button class="p-2 text-gray-400 hover:text-gray-500 relative">
                                <span class="sr-only">View notifications</span>
                                <svg class="h-6 w-6" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                                    <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M15 17h5l-5 5v-5zM10.5 3.75a6 6 0 0 1 6 6v2.25l2.25 2.25v.75H2.25v-.75L4.5 12V9.75a6 6 0 0 1 6-6z" />
                                </svg>
                                <!-- Notification badge -->
                                <span class="absolute -top-1 -right-1 h-4 w-4 bg-red-500 rounded-full text-xs text-white flex items-center justify-center">3</span>
                            </button>
                            
                            <!-- User dropdown -->
                            <div class="relative" x-data="{ userMenuOpen: false }">
                                <button @click="userMenuOpen = !userMenuOpen" 
                                        class="flex items-center space-x-2 p-2 rounded-md hover:bg-gray-100">
                                    {% if user.profile.avatar %}
                                        <img class="h-8 w-8 rounded-full object-cover" src="{{ user.profile.avatar.url }}" alt="{{ user.get_full_name|default:user.username }}">
                                    {% else %}
                                        <div class="h-8 w-8 bg-primary-500 rounded-full flex items-center justify-center">
                                            <span class="text-white text-sm font-medium">{{ user.first_name.0|default:user.username.0|upper }}</span>
                                        </div>
                                    {% endif %}
                                    <span class="hidden sm:block text-sm font-medium text-gray-700">{{ user.get_full_name|default:user.username }}</span>
                                    <svg class="h-4 w-4 text-gray-400" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                                        <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M19 9l-7 7-7-7" />
                                    </svg>
                                </button>
                                
                                <!-- Dropdown menu -->
                                <div x-show="userMenuOpen" 
                                     x-transition:enter="transition ease-out duration-100"
                                     x-transition:enter-start="transform opacity-0 scale-95"
                                     x-transition:enter-end="transform opacity-100 scale-100"
                                     x-transition:leave="transition ease-in duration-75"
                                     x-transition:leave-start="transform opacity-100 scale-100"
                                     x-transition:leave-end="transform opacity-0 scale-95"
                                     @click.away="userMenuOpen = false"
                                     class="absolute right-0 mt-2 w-48 bg-white rounded-md shadow-lg py-1 z-50 border border-gray-200">
                                    <a href="#" class="block px-4 py-2 text-sm text-gray-700 hover:bg-gray-100">Profile Settings</a>
                                    <a href="#" class="block px-4 py-2 text-sm text-gray-700 hover:bg-gray-100">Account Settings</a>
                                    <div class="border-t border-gray-100"></div>
                                    <form method="post" action="{% url 'logout' %}" class="block">
                                        {% csrf_token %}
                                        <button type="submit" class="w-full text-left px-4 py-2 text-sm text-gray-700 hover:bg-gray-100">Sign out</button>
                                    </form>
                                </div>
                            </div>
                        {% else %}
                            <!-- Login/Register buttons -->
                            <div class="flex items-center space-x-2">
                                <a href="{% url 'login' %}" class="text-sm font-medium text-gray-700 hover:text-gray-900">Sign in</a>
                                <a href="{% url 'register' %}" class="bg-primary-600 text-white px-4 py-2 rounded-md text-sm font-medium hover:bg-primary-700">Sign up</a>
                            </div>
                        {% endif %}
                    </div>
                </div>
            </header>
            
            <!-- Main content -->
            <main class="flex-1 overflow-y-auto">
                <!-- Flash messages -->
                {% if messages %}
                    <div class="p-4">
                        {% for message in messages %}
                            <div class="mb-4 p-4 rounded-md {% if message.tags == 'error' %}bg-red-50 text-red-800 border border-red-200{% elif message.tags == 'warning' %}bg-yellow-50 text-yellow-800 border border-yellow-200{% elif message.tags == 'success' %}bg-green-50 text-green-800 border border-green-200{% else %}bg-blue-50 text-blue-800 border border-blue-200{% endif %}"
                                 x-data="{ show: true }" 
                                 x-show="show"
                                 x-transition:enter="transition ease-out duration-300"
                                 x-transition:enter-start="opacity-0 transform scale-95"
                                 x-transition:enter-end="opacity-100 transform scale-100"
                                 x-transition:leave="transition ease-in duration-200"
                                 x-transition:leave-start="opacity-100 transform scale-100"
                                 x-transition:leave-end="opacity-0 transform scale-95">
                                <div class="flex items-center justify-between">
                                    <p class="text-sm font-medium">{{ message }}</p>
                                    <button @click="show = false" class="ml-4 text-current opacity-50 hover:opacity-75">
                                        <svg class="h-4 w-4" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                                            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M6 18L18 6M6 6l12 12" />
                                        </svg>
                                    </button>
                                </div>
                            </div>
                        {% endfor %}
                    </div>
                {% endif %}
                
                <!-- Page content -->
                {% block content %}{% endblock %}
            </main>
            
            <!-- Footer -->
            <footer class="bg-white border-t border-gray-200 mt-auto">
                <div class="max-w-7xl mx-auto px-4 py-6 sm:px-6 lg:px-8">
                    <div class="flex flex-col md:flex-row justify-between items-center space-y-4 md:space-y-0">
                        <div class="flex items-center space-x-2">
                            <div class="w-6 h-6 bg-primary-600 rounded flex items-center justify-center">
                                <span class="text-white font-bold text-sm">L</span>
                            </div>
                            <span class="text-sm text-gray-600">© 2025 LandHub. All rights reserved.</span>
                        </div>
                        <div class="flex items-center space-x-6 text-sm text-gray-600">
                            <a href="#" class="hover:text-gray-900">Privacy Policy</a>
                            <a href="#" class="hover:text-gray-900">Terms of Service</a>
                            <a href="#" class="hover:text-gray-900">Contact</a>
                        </div>
                    </div>
                </div>
            </footer>
        </div>
    </div>
    
    <!-- HTMX Configuration -->
    <script>
        document.body.addEventListener('htmx:configRequest', function(evt) {
            evt.detail.headers['X-CSRFToken'] = '{{ csrf_token }}';
        });
        
        // Show loading indicator on HTMX requests
        document.body.addEventListener('htmx:beforeRequest', function(evt) {
            document.getElementById('loading-indicator').style.transform = 'scaleX(1)';
        });
        
        document.body.addEventListener('htmx:afterRequest', function(evt) {
            setTimeout(() => {
                document.getElementById('loading-indicator').style.transform = 'scaleX(0)';
            }, 200);
        });
    </script>
    
    {% block extra_js %}{% endblock %}
</body>
</html>
