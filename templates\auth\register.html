{% extends 'base_landing.html' %}

{% block title %}Sign Up - LandHub{% endblock %}

{% block content %}
<!-- Background with gradient and pattern -->
<div class="min-h-screen bg-gradient-to-br from-primary-50 via-white to-primary-100 relative overflow-hidden">
    <!-- Animated background elements -->
    <div class="absolute inset-0 overflow-hidden">
        <div class="absolute -top-40 -right-32 w-80 h-80 bg-primary-200 rounded-full mix-blend-multiply filter blur-xl opacity-70 animate-blob"></div>
        <div class="absolute -bottom-40 -left-32 w-80 h-80 bg-green-200 rounded-full mix-blend-multiply filter blur-xl opacity-70 animate-blob animation-delay-2000"></div>
        <div class="absolute top-40 left-40 w-80 h-80 bg-primary-300 rounded-full mix-blend-multiply filter blur-xl opacity-70 animate-blob animation-delay-4000"></div>
    </div>

    <div class="relative flex items-center justify-center min-h-screen py-12 px-4 sm:px-6 lg:px-8">
        <div class="max-w-md w-full" x-data="registerForm()">
            <!-- Logo and Header -->
            <div class="text-center mb-8">
                <div class="mx-auto h-16 w-16 bg-gradient-to-br from-primary-600 to-primary-700 rounded-2xl flex items-center justify-center shadow-lg transform hover:scale-105 transition-transform duration-200">
                    <span class="text-white font-bold text-2xl">L</span>
                </div>
                <h2 class="mt-6 text-center text-3xl font-bold bg-gradient-to-r from-gray-900 to-gray-700 bg-clip-text text-transparent">
                    Join LandHub
                </h2>
                <p class="mt-2 text-center text-sm text-gray-600">
                    Create your account as a
                    <span class="font-semibold text-primary-600 px-2 py-1 bg-primary-50 rounded-full">
                        {% if role == 'seller' %}Seller{% elif role == 'admin' %}Administrator{% else %}Buyer{% endif %}
                    </span>
                </p>
            </div>

            <!-- Registration Form Card -->
            <div class="bg-white/80 backdrop-blur-sm rounded-2xl shadow-xl border border-white/20 p-8 space-y-6">
                <!-- Role Selection -->
                <div class="flex justify-center space-x-1 bg-gray-100 rounded-xl p-1">
                    <a href="{% url 'register' %}?role=buyer"
                       class="flex-1 text-center py-2 px-3 rounded-lg text-sm font-medium transition-all duration-200 {% if role == 'buyer' %}bg-white text-primary-600 shadow-sm{% else %}text-gray-600 hover:text-gray-900{% endif %}">
                        Buyer
                    </a>
                    <a href="{% url 'register' %}?role=seller"
                       class="flex-1 text-center py-2 px-3 rounded-lg text-sm font-medium transition-all duration-200 {% if role == 'seller' %}bg-white text-primary-600 shadow-sm{% else %}text-gray-600 hover:text-gray-900{% endif %}">
                        Seller
                    </a>
                </div>

                <!-- Social Registration Placeholder -->
                <div class="grid grid-cols-2 gap-3">
                    <button type="button" class="w-full inline-flex justify-center py-3 px-4 rounded-xl border border-gray-300 bg-white text-sm font-medium text-gray-500 hover:bg-gray-50 hover:border-gray-400 transition-all duration-200 transform hover:scale-105">
                        <svg class="w-5 h-5" viewBox="0 0 24 24">
                            <path fill="currentColor" d="M22.56 12.25c0-.78-.07-1.53-.2-2.25H12v4.26h5.92c-.26 1.37-1.04 2.53-2.21 3.31v2.77h3.57c2.08-1.92 3.28-4.74 3.28-8.09z"/>
                            <path fill="currentColor" d="M12 23c2.97 0 5.46-.98 7.28-2.66l-3.57-2.77c-.98.66-2.23 1.06-3.71 1.06-2.86 0-5.29-1.93-6.16-4.53H2.18v2.84C3.99 20.53 7.7 23 12 23z"/>
                            <path fill="currentColor" d="M5.84 14.09c-.22-.66-.35-1.36-.35-2.09s.13-1.43.35-2.09V7.07H2.18C1.43 8.55 1 10.22 1 12s.43 3.45 1.18 4.93l2.85-2.22.81-.62z"/>
                            <path fill="currentColor" d="M12 5.38c1.62 0 3.06.56 4.21 1.64l3.15-3.15C17.45 2.09 14.97 1 12 1 7.7 1 3.99 3.47 2.18 7.07l3.66 2.84c.87-2.6 3.3-4.53 6.16-4.53z"/>
                        </svg>
                        <span class="ml-2">Google</span>
                    </button>
                    <button type="button" class="w-full inline-flex justify-center py-3 px-4 rounded-xl border border-gray-300 bg-white text-sm font-medium text-gray-500 hover:bg-gray-50 hover:border-gray-400 transition-all duration-200 transform hover:scale-105">
                        <svg class="w-5 h-5" fill="currentColor" viewBox="0 0 24 24">
                            <path d="M24 12.073c0-6.627-5.373-12-12-12s-12 5.373-12 12c0 5.99 4.388 10.954 10.125 11.854v-8.385H7.078v-3.47h3.047V9.43c0-3.007 1.792-4.669 4.533-4.669 1.312 0 2.686.235 2.686.235v2.953H15.83c-1.491 0-1.956.925-1.956 1.874v2.25h3.328l-.532 3.47h-2.796v8.385C19.612 23.027 24 18.062 24 12.073z"/>
                        </svg>
                        <span class="ml-2">Facebook</span>
                    </button>
                </div>

                <!-- Divider -->
                <div class="relative">
                    <div class="absolute inset-0 flex items-center">
                        <div class="w-full border-t border-gray-300"></div>
                    </div>
                    <div class="relative flex justify-center text-sm">
                        <span class="px-2 bg-white text-gray-500">Or create with email</span>
                    </div>
                </div>

                <!-- Registration Form -->
                <form class="space-y-6" method="post" @submit="handleSubmit">
                    {% csrf_token %}

                    <!-- Username Field -->
                    <div class="space-y-1">
                        <label for="id_username" class="block text-sm font-medium text-gray-700">Username</label>
                        <div class="relative">
                            <div class="absolute inset-y-0 left-0 pl-3 flex items-center pointer-events-none">
                                <svg class="h-5 w-5 text-gray-400" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                                    <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M16 7a4 4 0 11-8 0 4 4 0 018 0zM12 14a7 7 0 00-7 7h14a7 7 0 00-7-7z" />
                                </svg>
                            </div>
                            <input id="id_username"
                                   name="username"
                                   type="text"
                                   required
                                   x-model="form.username"
                                   @input="clearError('username'); validateUsername()"
                                   class="block w-full pl-10 pr-3 py-3 border border-gray-300 rounded-xl placeholder-gray-400 focus:outline-none focus:ring-2 focus:ring-primary-500 focus:border-transparent transition-all duration-200 bg-gray-50 focus:bg-white"
                                   :class="{ 'border-red-300 bg-red-50': errors.username, 'border-green-300 bg-green-50': form.username && !errors.username }"
                                   placeholder="Choose a unique username">
                        </div>
                        <div x-show="errors.username" x-transition class="text-red-600 text-sm mt-1" x-text="errors.username"></div>
                        {% if form.username.errors %}
                            <div class="text-red-600 text-sm mt-1">{{ form.username.errors.0 }}</div>
                        {% endif %}
                    </div>

                    <!-- Password Field -->
                    <div class="space-y-1">
                        <label for="id_password1" class="block text-sm font-medium text-gray-700">Password</label>
                        <div class="relative">
                            <div class="absolute inset-y-0 left-0 pl-3 flex items-center pointer-events-none">
                                <svg class="h-5 w-5 text-gray-400" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                                    <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M12 15v2m-6 4h12a2 2 0 002-2v-6a2 2 0 00-2-2H6a2 2 0 00-2 2v6a2 2 0 002 2zm10-10V7a4 4 0 00-8 0v4h8z" />
                                </svg>
                            </div>
                            <input id="id_password1"
                                   name="password1"
                                   :type="showPassword ? 'text' : 'password'"
                                   required
                                   x-model="form.password1"
                                   @input="clearError('password1'); validatePassword()"
                                   class="block w-full pl-10 pr-12 py-3 border border-gray-300 rounded-xl placeholder-gray-400 focus:outline-none focus:ring-2 focus:ring-primary-500 focus:border-transparent transition-all duration-200 bg-gray-50 focus:bg-white"
                                   :class="{ 'border-red-300 bg-red-50': errors.password1 }"
                                   placeholder="Create a strong password">
                            <button type="button"
                                    @click="showPassword = !showPassword"
                                    class="absolute inset-y-0 right-0 pr-3 flex items-center">
                                <svg x-show="!showPassword" class="h-5 w-5 text-gray-400 hover:text-gray-600" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                                    <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M15 12a3 3 0 11-6 0 3 3 0 016 0z" />
                                    <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M2.458 12C3.732 7.943 7.523 5 12 5c4.478 0 8.268 2.943 9.542 7-1.274 4.057-5.064 7-9.542 7-4.477 0-8.268-2.943-9.542-7z" />
                                </svg>
                                <svg x-show="showPassword" class="h-5 w-5 text-gray-400 hover:text-gray-600" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                                    <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M13.875 18.825A10.05 10.05 0 0112 19c-4.478 0-8.268-2.943-9.543-7a9.97 9.97 0 011.563-3.029m5.858.908a3 3 0 114.243 4.243M9.878 9.878l4.242 4.242M9.878 9.878L3 3m6.878 6.878L21 21" />
                                </svg>
                            </button>
                        </div>

                        <!-- Password Strength Indicator -->
                        <div x-show="form.password1" x-transition class="mt-2">
                            <div class="flex items-center space-x-2">
                                <div class="flex-1 bg-gray-200 rounded-full h-2">
                                    <div class="h-2 rounded-full transition-all duration-300"
                                         :class="{
                                             'bg-red-500 w-1/4': passwordStrength === 'weak',
                                             'bg-yellow-500 w-2/4': passwordStrength === 'medium',
                                             'bg-green-500 w-3/4': passwordStrength === 'strong',
                                             'bg-green-600 w-full': passwordStrength === 'very-strong'
                                         }"></div>
                                </div>
                                <span class="text-xs font-medium"
                                      :class="{
                                          'text-red-600': passwordStrength === 'weak',
                                          'text-yellow-600': passwordStrength === 'medium',
                                          'text-green-600': passwordStrength === 'strong',
                                          'text-green-700': passwordStrength === 'very-strong'
                                      }"
                                      x-text="passwordStrength.charAt(0).toUpperCase() + passwordStrength.slice(1).replace('-', ' ')"></span>
                            </div>
                        </div>

                        <div x-show="errors.password1" x-transition class="text-red-600 text-sm mt-1" x-text="errors.password1"></div>
                        {% if form.password1.errors %}
                            <div class="text-red-600 text-sm mt-1">{{ form.password1.errors.0 }}</div>
                        {% endif %}
                    </div>

                    <!-- Confirm Password Field -->
                    <div class="space-y-1">
                        <label for="id_password2" class="block text-sm font-medium text-gray-700">Confirm Password</label>
                        <div class="relative">
                            <div class="absolute inset-y-0 left-0 pl-3 flex items-center pointer-events-none">
                                <svg class="h-5 w-5 text-gray-400" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                                    <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M9 12l2 2 4-4m6 2a9 9 0 11-18 0 9 9 0 0118 0z" />
                                </svg>
                            </div>
                            <input id="id_password2"
                                   name="password2"
                                   :type="showConfirmPassword ? 'text' : 'password'"
                                   required
                                   x-model="form.password2"
                                   @input="clearError('password2'); validatePasswordMatch()"
                                   class="block w-full pl-10 pr-12 py-3 border border-gray-300 rounded-xl placeholder-gray-400 focus:outline-none focus:ring-2 focus:ring-primary-500 focus:border-transparent transition-all duration-200 bg-gray-50 focus:bg-white"
                                   :class="{ 'border-red-300 bg-red-50': errors.password2, 'border-green-300 bg-green-50': form.password2 && form.password1 === form.password2 && !errors.password2 }"
                                   placeholder="Confirm your password">
                            <button type="button"
                                    @click="showConfirmPassword = !showConfirmPassword"
                                    class="absolute inset-y-0 right-0 pr-3 flex items-center">
                                <svg x-show="!showConfirmPassword" class="h-5 w-5 text-gray-400 hover:text-gray-600" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                                    <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M15 12a3 3 0 11-6 0 3 3 0 016 0z" />
                                    <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M2.458 12C3.732 7.943 7.523 5 12 5c4.478 0 8.268 2.943 9.542 7-1.274 4.057-5.064 7-9.542 7-4.477 0-8.268-2.943-9.542-7z" />
                                </svg>
                                <svg x-show="showConfirmPassword" class="h-5 w-5 text-gray-400 hover:text-gray-600" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                                    <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M13.875 18.825A10.05 10.05 0 0112 19c-4.478 0-8.268-2.943-9.543-7a9.97 9.97 0 011.563-3.029m5.858.908a3 3 0 114.243 4.243M9.878 9.878l4.242 4.242M9.878 9.878L3 3m6.878 6.878L21 21" />
                                </svg>
                            </button>
                        </div>
                        <div x-show="errors.password2" x-transition class="text-red-600 text-sm mt-1" x-text="errors.password2"></div>
                        {% if form.password2.errors %}
                            <div class="text-red-600 text-sm mt-1">{{ form.password2.errors.0 }}</div>
                        {% endif %}
                    </div>

                    <!-- Terms and Conditions -->
                    <div class="flex items-start">
                        <div class="flex items-center h-5">
                            <input id="terms"
                                   name="terms"
                                   type="checkbox"
                                   required
                                   x-model="form.terms"
                                   class="h-4 w-4 text-primary-600 focus:ring-primary-500 border-gray-300 rounded transition-colors duration-200">
                        </div>
                        <div class="ml-3 text-sm">
                            <label for="terms" class="text-gray-700">
                                I agree to the
                                <a href="#" class="text-primary-600 hover:text-primary-500 font-medium">Terms of Service</a>
                                and
                                <a href="#" class="text-primary-600 hover:text-primary-500 font-medium">Privacy Policy</a>
                            </label>
                        </div>
                    </div>

                    <!-- Submit Button -->
                    <div>
                        <button type="submit"
                                :disabled="loading || !isFormValid"
                                class="group relative w-full flex justify-center py-3 px-4 border border-transparent text-sm font-medium rounded-xl text-white bg-gradient-to-r from-primary-600 to-primary-700 hover:from-primary-700 hover:to-primary-800 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-primary-500 disabled:opacity-50 disabled:cursor-not-allowed transition-all duration-200 transform hover:scale-105 shadow-lg hover:shadow-xl"
                                :class="{ 'animate-pulse': loading }">
                            <span x-show="!loading" class="flex items-center">
                                <svg class="w-5 h-5 mr-2 group-hover:animate-pulse" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                                    <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M18 9v3m0 0v3m0-3h3m-3 0h-3m-2-5a4 4 0 11-8 0 4 4 0 018 0zM3 20a6 6 0 0112 0v1H3v-1z" />
                                </svg>
                                Create Your Account
                            </span>
                            <span x-show="loading" class="flex items-center">
                                <svg class="animate-spin -ml-1 mr-3 h-5 w-5 text-white" xmlns="http://www.w3.org/2000/svg" fill="none" viewBox="0 0 24 24">
                                    <circle class="opacity-25" cx="12" cy="12" r="10" stroke="currentColor" stroke-width="4"></circle>
                                    <path class="opacity-75" fill="currentColor" d="M4 12a8 8 0 018-8V0C5.373 0 0 5.373 0 12h4zm2 5.291A7.962 7.962 0 014 12H0c0 3.042 1.135 5.824 3 7.938l3-2.647z"></path>
                                </svg>
                                Creating Account...
                            </span>
                        </button>
                    </div>
                </form>

                <!-- Login Link -->
                <div class="text-center">
                    <p class="text-sm text-gray-600">
                        Already have an account?
                        <a href="{% url 'login' %}" class="font-medium text-primary-600 hover:text-primary-500 transition-colors duration-200">
                            Sign in instead
                        </a>
                    </p>
                </div>
            </div>

            <!-- Additional Info -->
            <div class="mt-8 text-center">
                <p class="text-xs text-gray-500">
                    By creating an account, you agree to our
                    <a href="#" class="text-primary-600 hover:text-primary-500">Terms of Service</a>
                    and
                    <a href="#" class="text-primary-600 hover:text-primary-500">Privacy Policy</a>
                </p>
            </div>
        </div>
    </div>
</div>

<script>
function registerForm() {
    return {
        form: {
            username: '',
            password1: '',
            password2: '',
            terms: false
        },
        errors: {},
        loading: false,
        showPassword: false,
        showConfirmPassword: false,
        passwordStrength: 'weak',

        get isFormValid() {
            return this.form.username.trim() &&
                   this.form.password1 &&
                   this.form.password2 &&
                   this.form.password1 === this.form.password2 &&
                   this.form.terms &&
                   Object.keys(this.errors).length === 0;
        },

        handleSubmit(event) {
            this.loading = true;
            this.errors = {};

            // Validate all fields
            this.validateUsername();
            this.validatePassword();
            this.validatePasswordMatch();

            if (!this.form.terms) {
                this.errors.terms = 'You must agree to the terms and conditions';
            }

            if (Object.keys(this.errors).length > 0) {
                this.loading = false;
                event.preventDefault();
                return;
            }

            // Form will submit normally if no errors
        },

        validateUsername() {
            if (!this.form.username.trim()) {
                this.errors.username = 'Username is required';
            } else if (this.form.username.length < 3) {
                this.errors.username = 'Username must be at least 3 characters';
            } else if (!/^[a-zA-Z0-9_]+$/.test(this.form.username)) {
                this.errors.username = 'Username can only contain letters, numbers, and underscores';
            } else {
                delete this.errors.username;
            }
        },

        validatePassword() {
            if (!this.form.password1) {
                this.errors.password1 = 'Password is required';
                this.passwordStrength = 'weak';
            } else {
                delete this.errors.password1;
                this.calculatePasswordStrength();
            }
        },

        validatePasswordMatch() {
            if (!this.form.password2) {
                this.errors.password2 = 'Please confirm your password';
            } else if (this.form.password1 !== this.form.password2) {
                this.errors.password2 = 'Passwords do not match';
            } else {
                delete this.errors.password2;
            }
        },

        calculatePasswordStrength() {
            const password = this.form.password1;
            let score = 0;

            // Length check
            if (password.length >= 8) score++;
            if (password.length >= 12) score++;

            // Character variety checks
            if (/[a-z]/.test(password)) score++;
            if (/[A-Z]/.test(password)) score++;
            if (/[0-9]/.test(password)) score++;
            if (/[^A-Za-z0-9]/.test(password)) score++;

            if (score <= 2) {
                this.passwordStrength = 'weak';
            } else if (score <= 4) {
                this.passwordStrength = 'medium';
            } else if (score <= 5) {
                this.passwordStrength = 'strong';
            } else {
                this.passwordStrength = 'very-strong';
            }
        },

        clearError(field) {
            if (this.errors[field]) {
                delete this.errors[field];
            }
        }
    }
}
</script>

<style>
@keyframes blob {
    0% {
        transform: translate(0px, 0px) scale(1);
    }
    33% {
        transform: translate(30px, -50px) scale(1.1);
    }
    66% {
        transform: translate(-20px, 20px) scale(0.9);
    }
    100% {
        transform: translate(0px, 0px) scale(1);
    }
}

.animate-blob {
    animation: blob 7s infinite;
}

.animation-delay-2000 {
    animation-delay: 2s;
}

.animation-delay-4000 {
    animation-delay: 4s;
}
</style>
{% endblock %}
