{% extends 'base_landing.html' %}
{% load static %}

{% block title %}LandHub - Find Your Perfect Land{% endblock %}
{% block meta_description %}Discover premium land properties for sale. Browse residential, commercial, agricultural, and recreational land listings on LandHub - your trusted land real estate platform.{% endblock %}

{% block content %}
<!-- Hero Section -->
<section class="relative bg-gradient-to-br from-primary-600 to-primary-800 overflow-hidden">
    <!-- Background pattern -->
    <div class="absolute inset-0 bg-black opacity-20"></div>
    <div class="absolute inset-0" style="background-image: url('data:image/svg+xml,%3Csvg width="60" height="60" viewBox="0 0 60 60" xmlns="http://www.w3.org/2000/svg"%3E%3Cg fill="none" fill-rule="evenodd"%3E%3Cg fill="%23ffffff" fill-opacity="0.1"%3E%3Ccircle cx="30" cy="30" r="2"/%3E%3C/g%3E%3C/g%3E%3C/svg%3E');"></div>
    
    <div class="relative max-w-7xl mx-auto px-4 py-16 sm:px-6 sm:py-24 lg:px-8 lg:py-32">
        <div class="text-center">
            <h1 class="text-4xl font-bold tracking-tight text-white sm:text-5xl lg:text-6xl">
                Find Your Perfect
                <span class="block text-primary-200">Land Investment</span>
            </h1>
            <p class="mt-6 max-w-2xl mx-auto text-xl text-primary-100 leading-relaxed">
                Discover premium land properties across the country. From residential lots to agricultural acres, find the perfect piece of land for your next investment or dream project.
            </p>
            
            <!-- Search Bar -->
            <div class="mt-10 max-w-2xl mx-auto">
                <form class="flex flex-col sm:flex-row gap-4" hx-get="/search/" hx-target="#search-results" hx-trigger="submit">
                    <div class="flex-1">
                        <label for="search-location" class="sr-only">Search location</label>
                        <input type="text" 
                               id="search-location" 
                               name="location" 
                               placeholder="Enter city, state, or zip code"
                               class="w-full px-4 py-3 text-gray-900 placeholder-gray-500 bg-white border border-transparent rounded-lg focus:outline-none focus:ring-2 focus:ring-primary-500 focus:border-transparent text-lg">
                    </div>
                    <button type="submit" 
                            class="px-8 py-3 bg-primary-500 text-white font-semibold rounded-lg hover:bg-primary-400 focus:outline-none focus:ring-2 focus:ring-primary-500 focus:ring-offset-2 focus:ring-offset-primary-600 transition-colors duration-200 text-lg">
                        Search Land
                    </button>
                </form>
            </div>
            
            <!-- Quick Stats -->
            <div class="mt-12 grid grid-cols-2 gap-4 sm:grid-cols-4 lg:gap-8">
                <div class="text-center">
                    <div class="text-3xl font-bold text-white">1,250+</div>
                    <div class="text-primary-200 text-sm">Active Listings</div>
                </div>
                <div class="text-center">
                    <div class="text-3xl font-bold text-white">850+</div>
                    <div class="text-primary-200 text-sm">Happy Buyers</div>
                </div>
                <div class="text-center">
                    <div class="text-3xl font-bold text-white">500+</div>
                    <div class="text-primary-200 text-sm">Trusted Sellers</div>
                </div>
                <div class="text-center">
                    <div class="text-3xl font-bold text-white">45</div>
                    <div class="text-primary-200 text-sm">States Covered</div>
                </div>
            </div>
        </div>
    </div>
</section>

<!-- Property Types Section -->
<section class="py-16 bg-white">
    <div class="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
        <div class="text-center mb-12">
            <h2 class="text-3xl font-bold text-gray-900 sm:text-4xl">Browse by Property Type</h2>
            <p class="mt-4 text-xl text-gray-600">Find the perfect land for your specific needs</p>
        </div>
        
        <div class="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-8">
            <!-- Residential -->
            <div class="group cursor-pointer" hx-get="/browse/?property_type=residential" hx-target="#main-content" hx-push-url="true">
                <div class="relative overflow-hidden rounded-lg bg-gradient-to-br from-blue-500 to-blue-600 p-8 text-white transition-transform duration-300 group-hover:scale-105">
                    <div class="absolute top-4 right-4">
                        <svg class="h-8 w-8 text-blue-200" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M3 12l2-2m0 0l7-7 7 7M5 10v10a1 1 0 001 1h3m10-11l2 2m-2-2v10a1 1 0 01-1 1h-3m-6 0a1 1 0 001-1v-4a1 1 0 011-1h2a1 1 0 011 1v4a1 1 0 001 1m-6 0h6" />
                        </svg>
                    </div>
                    <h3 class="text-xl font-semibold mb-2">Residential</h3>
                    <p class="text-blue-100 text-sm">Perfect lots for building your dream home</p>
                    <div class="mt-4 text-2xl font-bold">320+ listings</div>
                </div>
            </div>
            
            <!-- Commercial -->
            <div class="group cursor-pointer" hx-get="/browse/?property_type=commercial" hx-target="#main-content" hx-push-url="true">
                <div class="relative overflow-hidden rounded-lg bg-gradient-to-br from-purple-500 to-purple-600 p-8 text-white transition-transform duration-300 group-hover:scale-105">
                    <div class="absolute top-4 right-4">
                        <svg class="h-8 w-8 text-purple-200" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M19 21V5a2 2 0 00-2-2H7a2 2 0 00-2 2v16m14 0h2m-2 0h-5m-9 0H3m2 0h5M9 7h1m-1 4h1m4-4h1m-1 4h1m-5 10v-5a1 1 0 011-1h2a1 1 0 011 1v5m-4 0h4" />
                        </svg>
                    </div>
                    <h3 class="text-xl font-semibold mb-2">Commercial</h3>
                    <p class="text-purple-100 text-sm">Prime locations for business development</p>
                    <div class="mt-4 text-2xl font-bold">180+ listings</div>
                </div>
            </div>
            
            <!-- Agricultural -->
            <div class="group cursor-pointer" hx-get="/browse/?property_type=agricultural" hx-target="#main-content" hx-push-url="true">
                <div class="relative overflow-hidden rounded-lg bg-gradient-to-br from-green-500 to-green-600 p-8 text-white transition-transform duration-300 group-hover:scale-105">
                    <div class="absolute top-4 right-4">
                        <svg class="h-8 w-8 text-green-200" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M4.318 6.318a4.5 4.5 0 000 6.364L12 20.364l7.682-7.682a4.5 4.5 0 00-6.364-6.364L12 7.636l-1.318-1.318a4.5 4.5 0 00-6.364 0z" />
                        </svg>
                    </div>
                    <h3 class="text-xl font-semibold mb-2">Agricultural</h3>
                    <p class="text-green-100 text-sm">Fertile farmland and ranch properties</p>
                    <div class="mt-4 text-2xl font-bold">450+ listings</div>
                </div>
            </div>
            
            <!-- Recreational -->
            <div class="group cursor-pointer" hx-get="/browse/?property_type=recreational" hx-target="#main-content" hx-push-url="true">
                <div class="relative overflow-hidden rounded-lg bg-gradient-to-br from-orange-500 to-orange-600 p-8 text-white transition-transform duration-300 group-hover:scale-105">
                    <div class="absolute top-4 right-4">
                        <svg class="h-8 w-8 text-orange-200" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M5 3v4M3 5h4M6 17v4m-2-2h4m5-16l2.286 6.857L21 12l-5.714 2.143L13 21l-2.286-6.857L5 12l5.714-2.143L13 3z" />
                        </svg>
                    </div>
                    <h3 class="text-xl font-semibold mb-2">Recreational</h3>
                    <p class="text-orange-100 text-sm">Hunting, camping, and outdoor adventure land</p>
                    <div class="mt-4 text-2xl font-bold">300+ listings</div>
                </div>
            </div>
        </div>
    </div>
</section>

<!-- Featured Listings Section -->
<section class="py-16 bg-gray-50">
    <div class="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
        <div class="text-center mb-12">
            <h2 class="text-3xl font-bold text-gray-900 sm:text-4xl">Featured Properties</h2>
            <p class="mt-4 text-xl text-gray-600">Hand-picked premium land listings</p>
        </div>
        
        <!-- Featured listings will be loaded via HTMX -->
        <div id="featured-listings" 
             hx-get="/api/featured-listings/" 
             hx-trigger="load"
             hx-indicator="#featured-loading">
            <!-- Loading state -->
            <div id="featured-loading" class="flex justify-center items-center py-12">
                <div class="animate-spin rounded-full h-12 w-12 border-b-2 border-primary-600"></div>
            </div>
        </div>
        
        <div class="text-center mt-12">
            <a href="/browse/" 
               class="inline-flex items-center px-6 py-3 border border-transparent text-base font-medium rounded-md text-white bg-primary-600 hover:bg-primary-700 transition-colors duration-200">
                View All Properties
                <svg class="ml-2 -mr-1 w-5 h-5" fill="currentColor" viewBox="0 0 20 20">
                    <path fill-rule="evenodd" d="M10.293 3.293a1 1 0 011.414 0l6 6a1 1 0 010 1.414l-6 6a1 1 0 01-1.414-1.414L14.586 11H3a1 1 0 110-2h11.586l-4.293-4.293a1 1 0 010-1.414z" clip-rule="evenodd"></path>
                </svg>
            </a>
        </div>
    </div>
</section>

<!-- How It Works Section -->
<section class="py-16 bg-white">
    <div class="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
        <div class="text-center mb-12">
            <h2 class="text-3xl font-bold text-gray-900 sm:text-4xl">How LandHub Works</h2>
            <p class="mt-4 text-xl text-gray-600">Simple steps to find or sell your perfect land</p>
        </div>
        
        <div class="grid grid-cols-1 md:grid-cols-3 gap-8">
            <!-- Step 1 -->
            <div class="text-center">
                <div class="mx-auto w-16 h-16 bg-primary-100 rounded-full flex items-center justify-center mb-6">
                    <span class="text-2xl font-bold text-primary-600">1</span>
                </div>
                <h3 class="text-xl font-semibold text-gray-900 mb-4">Search & Filter</h3>
                <p class="text-gray-600">Use our advanced search tools to find land that matches your specific criteria including location, size, price, and property type.</p>
            </div>
            
            <!-- Step 2 -->
            <div class="text-center">
                <div class="mx-auto w-16 h-16 bg-primary-100 rounded-full flex items-center justify-center mb-6">
                    <span class="text-2xl font-bold text-primary-600">2</span>
                </div>
                <h3 class="text-xl font-semibold text-gray-900 mb-4">Connect & Inquire</h3>
                <p class="text-gray-600">Contact sellers directly through our secure messaging system. Ask questions, schedule visits, and get all the information you need.</p>
            </div>
            
            <!-- Step 3 -->
            <div class="text-center">
                <div class="mx-auto w-16 h-16 bg-primary-100 rounded-full flex items-center justify-center mb-6">
                    <span class="text-2xl font-bold text-primary-600">3</span>
                </div>
                <h3 class="text-xl font-semibold text-gray-900 mb-4">Close the Deal</h3>
                <p class="text-gray-600">Work with trusted professionals to complete your land purchase or sale. We provide resources to make the process smooth and secure.</p>
            </div>
        </div>
    </div>
</section>

<!-- CTA Section -->
<section class="py-16 bg-primary-600">
    <div class="max-w-4xl mx-auto text-center px-4 sm:px-6 lg:px-8">
        <h2 class="text-3xl font-bold text-white sm:text-4xl">Ready to Find Your Land?</h2>
        <p class="mt-4 text-xl text-primary-100">Join thousands of satisfied buyers and sellers on LandHub</p>
        
        <div class="mt-8 flex flex-col sm:flex-row gap-4 justify-center">
            {% if not user.is_authenticated %}
                <a href="/register/?role=buyer" 
                   class="inline-flex items-center px-8 py-3 border border-transparent text-base font-medium rounded-md text-primary-600 bg-white hover:bg-gray-50 transition-colors duration-200">
                    Start as Buyer
                </a>
                <a href="/register/?role=seller" 
                   class="inline-flex items-center px-8 py-3 border-2 border-white text-base font-medium rounded-md text-white hover:bg-primary-700 transition-colors duration-200">
                    Start as Seller
                </a>
            {% else %}
                {% if user.profile.role == 'buyer' %}
                    <a href="/browse/" 
                       class="inline-flex items-center px-8 py-3 border border-transparent text-base font-medium rounded-md text-primary-600 bg-white hover:bg-gray-50 transition-colors duration-200">
                        Browse Properties
                    </a>
                {% elif user.profile.role == 'seller' %}
                    <a href="/listings/create/" 
                       class="inline-flex items-center px-8 py-3 border border-transparent text-base font-medium rounded-md text-primary-600 bg-white hover:bg-gray-50 transition-colors duration-200">
                        List Your Property
                    </a>
                {% endif %}
                <a href="/dashboard/" 
                   class="inline-flex items-center px-8 py-3 border-2 border-white text-base font-medium rounded-md text-white hover:bg-primary-700 transition-colors duration-200">
                    Go to Dashboard
                </a>
            {% endif %}
        </div>
    </div>
</section>

<!-- Search Results Container (hidden by default) -->
<div id="search-results" class="hidden"></div>
{% endblock %}

{% block extra_js %}
<script>
    // Initialize any landing page specific JavaScript
    document.addEventListener('DOMContentLoaded', function() {
        // Add smooth scrolling for anchor links
        document.querySelectorAll('a[href^="#"]').forEach(anchor => {
            anchor.addEventListener('click', function (e) {
                e.preventDefault();
                const target = document.querySelector(this.getAttribute('href'));
                if (target) {
                    target.scrollIntoView({
                        behavior: 'smooth',
                        block: 'start'
                    });
                }
            });
        });
    });
</script>
{% endblock %}
